<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="basepopup_error_decorview">PopupWindow needs %s of DecorView\'s WindowToken, please check if DecorView exists.</string>
    <string name="basepopup_error_destroyed">The BasePopup has been Destroyed and cannot continue to perform show.</string>
    <string name="basepopup_error_non_act_context">No host Activity found, please ensure you have at least one Activity open.</string>
    <string name="basepopup_error_thread">Please operate in the main thread.</string>
    <string name="basepopup_has_been_shown">BasePopup has been shown.</string>
    <string name="basepopup_host">popupwindow host (%s)</string>
    <string name="basepopup_host_destroyed">The popupwindow\'s host has been destroyed</string>
    <string name="basepopup_shown_successful">Show successful</string>
    <string name="basepopup_window_not_prepare">The %s window is not ready, wait for it to pop up when ready</string>
    <string name="basepopup_window_prepared">The %s window is ready to execute the popup.</string>
    <string name="versionchecklib_cancel">Cancel</string>
    <string name="versionchecklib_check_new_version">New Update</string>
    <string name="versionchecklib_confirm">Commit</string>
    <string name="versionchecklib_download_apkname">%s.apk</string>
    <string name="versionchecklib_download_fail">download failed，click to retry</string>
    <string name="versionchecklib_download_fail_retry">Download failed and retry？</string>
    <string name="versionchecklib_download_finish">download successful,click to install</string>
    <string name="versionchecklib_download_progress">download progress:%d%%/100%%</string>
    <string name="versionchecklib_downloading">downloading...</string>
    <string name="versionchecklib_progress">%d/100</string>
    <string name="versionchecklib_retry">Retry</string>
    <string name="versionchecklib_version_service_runing">application update service is running</string>
    <string name="versionchecklib_write_permission_deny">Download apk needs write permission。</string>
</resources>
