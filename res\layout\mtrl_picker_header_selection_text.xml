<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="start|bottom"
    android:layout_gravity="start|top"
    android:id="@+id/mtrl_picker_header_selection_text"
    android:paddingBottom="@dimen/mtrl_calendar_pre_l_text_clip_padding"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:firstBaselineToTopHeight="@dimen/mtrl_calendar_selection_text_baseline_to_top"
    app:lineHeight="@dimen/mtrl_calendar_header_selection_line_height"
    style="?attr/materialCalendarHeaderSelection"/>
