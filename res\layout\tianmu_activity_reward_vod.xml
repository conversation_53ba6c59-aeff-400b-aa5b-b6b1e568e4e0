<?xml version="1.0" encoding="utf-8"?>
<com.tianmu.biz.widget.RewardParentLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tianmu_library_rl_parent"
    android:background="#000000"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:gravity="center_vertical"
        android:id="@+id/tianmu_library_rl_video_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <TextView
        android:textSize="13sp"
        android:textColor="#ffffff"
        android:gravity="center"
        android:id="@+id/tianmu_library_tv_count_down"
        android:background="@drawable/tianmu_shape_75cccccc_circle"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:visibility="gone"
        android:layout_width="156dp"
        android:layout_height="24dp"
        android:layout_marginRight="74dp"
        android:text="@string/tianmu_reward_achieve_count_down"
        android:layout_alignTop="@+id/tianmu_library_iv_mute"
        android:layout_alignParentRight="true"/>
    <ImageView
        android:id="@+id/tianmu_library_iv_mute"
        android:background="@drawable/tianmu_shape_75cccccc_circle"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginLeft="24dp"
        android:layout_marginTop="44dp"
        android:src="@drawable/tianmu_reward_voice"
        android:scaleType="centerCrop"/>
    <include layout="@layout/tianmu_include_reward_vod_action_bar"/>
    <ProgressBar
        android:id="@+id/tianmu_library_progress_bar"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"/>
    <FrameLayout
        android:id="@+id/tianmu_library_full_screen_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <ImageView
        android:id="@+id/tianmu_library_iv_skip"
        android:background="@drawable/tianmu_shape_75cccccc_circle"
        android:visibility="invisible"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginRight="24dp"
        android:src="@drawable/tianmu_reward_close"
        android:layout_alignTop="@+id/tianmu_library_iv_mute"
        android:layout_alignParentRight="true"/>
    <FrameLayout
        android:id="@+id/tianmu_library_fl_reward_detention_dialog_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</com.tianmu.biz.widget.RewardParentLayout>
