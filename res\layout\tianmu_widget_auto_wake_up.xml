<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:background="@drawable/tianmu_shape_96000000_radius7"
    android:paddingLeft="14dp"
    android:paddingRight="11dp"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minWidth="260dp"
    android:minHeight="58dp">
    <com.tianmu.biz.widget.roundimage.RoundImageView
        android:id="@+id/tianmu_riv_logo"
        android:visibility="gone"
        android:layout_width="44dp"
        android:layout_height="42dp"/>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="9dp"
        android:layout_weight="1">
        <TextView
            android:textSize="16sp"
            android:textColor="#ffffff"
            android:gravity="center_vertical"
            android:id="@+id/tianmu_tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3S后打开查看"/>
        <TextView
            android:textSize="12sp"
            android:textColor="#c3c3c3"
            android:gravity="center_vertical"
            android:id="@+id/tianmu_tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="可查看更多精彩"/>
    </LinearLayout>
    <TextView
        android:textSize="12sp"
        android:textColor="#d4d4d4"
        android:gravity="center"
        android:id="@+id/tianmu_tv_close"
        android:background="@drawable/tianmu_shape_555454_radius7"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="57dp"
        android:minHeight="23dp"
        android:text="不用了"/>
</LinearLayout>
