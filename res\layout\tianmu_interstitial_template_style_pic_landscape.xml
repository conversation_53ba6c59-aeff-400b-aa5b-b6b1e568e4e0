<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/tianmu_interstitial_full_screen_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:id="@+id/tianmu_interstitial_fl_click"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignLeft="@+id/tianmu_interstitial_container"
        android:layout_alignTop="@+id/tianmu_interstitial_container"
        android:layout_alignRight="@+id/tianmu_interstitial_container"
        android:layout_alignBottom="@+id/tianmu_interstitial_container"/>
    <RelativeLayout
        android:orientation="vertical"
        android:id="@+id/tianmu_interstitial_container"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:layout_centerInParent="true">
        <ImageView
            android:id="@+id/tianmu_interstitial_iv_pic"
            android:background="#000000"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitCenter"/>
        <RelativeLayout
            android:id="@+id/tianmu_rl_ad_interact"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignLeft="@+id/tianmu_interstitial_iv_pic"
            android:layout_alignBottom="@+id/tianmu_interstitial_iv_pic"/>
        <TextView
            android:id="@+id/tianmu_tv_ad_target"
            android:src="@drawable/tianmu_icon_platform_icon"
            android:layout_alignRight="@+id/tianmu_interstitial_iv_pic"
            android:layout_alignBottom="@+id/tianmu_interstitial_iv_pic"
            style="@style/tianmu_ad_target_base_style"/>
    </RelativeLayout>
</RelativeLayout>
