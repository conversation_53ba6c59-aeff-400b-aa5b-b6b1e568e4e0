<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/tianmu_video_thumb"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"/>
    <ImageView
        android:layout_gravity="center"
        android:id="@+id/tianmu_video_start_play"
        android:background="@drawable/tianmu_video_shape_play_bg"
        android:padding="@dimen/tianmu_video_default_spacing"
        android:layout_width="@dimen/tianmu_video_play_btn_size"
        android:layout_height="@dimen/tianmu_video_play_btn_size"
        android:src="@drawable/tianmu_video_selector_play_button"/>
    <ProgressBar
        android:layout_gravity="center"
        android:id="@+id/tianmu_video_loading"
        android:visibility="gone"
        android:layout_width="@dimen/tianmu_video_play_btn_size"
        android:layout_height="@dimen/tianmu_video_play_btn_size"
        android:indeterminateDrawable="@drawable/tianmu_video_progress_loading"
        android:indeterminateDuration="2000"/>
    <FrameLayout
        android:id="@+id/tianmu_video_net_warning_layout"
        android:background="@android:color/black"
        android:focusable="true"
        android:visibility="gone"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <TextView
                android:textColor="@android:color/white"
                android:gravity="center"
                android:id="@+id/tianmu_video_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tianmu_video_wifi_tip"/>
            <TextView
                android:textColor="@android:color/white"
                android:gravity="center"
                android:id="@+id/tianmu_video_status_btn"
                android:background="@drawable/tianmu_video_shape_status_view_btn"
                android:paddingLeft="16dp"
                android:paddingTop="4dp"
                android:paddingRight="16dp"
                android:paddingBottom="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/tianmu_video_continue_play"/>
        </LinearLayout>
    </FrameLayout>
</FrameLayout>
