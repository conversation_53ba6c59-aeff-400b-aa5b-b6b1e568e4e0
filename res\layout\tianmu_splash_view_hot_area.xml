<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:orientation="horizontal"
    android:id="@+id/tianmu_splash_ll_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.tianmu.biz.widget.gravityrotation.GravityRotationView
        android:gravity="center"
        android:orientation="horizontal"
        android:id="@+id/tianmu_gravity_front"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:gravity="center"
            android:id="@+id/tianmu_splash_action_button_container"
            android:background="@drawable/tianmu_bg_splash_action_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="18sp"
                android:textColor="#ffffff"
                android:gravity="center"
                android:id="@+id/tianmu_splash_tv_action_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableRight="@drawable/tianmu_icon_right_arrow"
                android:layout_centerVertical="true"/>
        </LinearLayout>
    </com.tianmu.biz.widget.gravityrotation.GravityRotationView>
</LinearLayout>
