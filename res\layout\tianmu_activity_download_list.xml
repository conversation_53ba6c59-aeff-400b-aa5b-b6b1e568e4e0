<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <include layout="@layout/tianmu_include_normal_action_bar"/>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:gravity="center"
            android:orientation="vertical"
            android:id="@+id/tianmu_ll_download_list_empty"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true">
            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginLeft="3dp"
                android:src="@drawable/tianmu_icon_ad_install"/>
            <TextView
                android:textColor="#a4a4a4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="暂无下载"/>
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/tianmu_rv_download_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </RelativeLayout>
</LinearLayout>
