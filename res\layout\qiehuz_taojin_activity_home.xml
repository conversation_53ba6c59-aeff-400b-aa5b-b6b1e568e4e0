<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:id="@+id/title_bar"
        android:background="#f44848"
        android:layout_width="match_parent"
        android:layout_height="40dp">
        <ImageView
            android:id="@+id/title_back_btn"
            android:padding="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/qiehuz_taojin_title_back"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"/>
        <TextView
            android:textSize="36px"
            android:textColor="#fff"
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="企鹅赚钱"
            android:layout_centerInParent="true"/>
    </RelativeLayout>
    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</LinearLayout>
