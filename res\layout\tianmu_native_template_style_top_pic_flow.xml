<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tianmu_rl_ad_container"
    android:paddingLeft="15dp"
    android:paddingTop="16dp"
    android:paddingRight="15dp"
    android:paddingBottom="12dp"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <FrameLayout
        android:id="@+id/tianmu_fl_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <FrameLayout
        android:id="@+id/tianmu_fl_interaction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/tianmu_fl_container"
        android:layout_alignTop="@+id/tianmu_fl_container"
        android:layout_alignRight="@+id/tianmu_fl_container"
        android:layout_alignBottom="@+id/tianmu_fl_container"/>
    <TextView
        android:id="@+id/tianmu_tv_ad_target"
        android:layout_alignParentRight="true"
        style="@style/tianmu_ad_target_base_style"/>
    <TextView
        android:id="@+id/tianmu_banner_tv_ad_source"
        android:layout_alignLeft="@+id/tianmu_fl_container"
        android:layout_alignBottom="@+id/tianmu_fl_container"
        style="@style/tianmu_ad_source_base_style"/>
    <ImageView
        android:id="@+id/tianmu_iv_video_mute"
        android:src="@drawable/tianmu_reward_voice"
        android:layout_alignLeft="@+id/tianmu_fl_container"
        android:layout_alignBottom="@+id/tianmu_fl_container"
        style="@style/tianmu_iv_video_mute_style"/>
    <TextView
        android:textSize="18sp"
        android:ellipsize="end"
        android:id="@+id/tianmu_tv_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/tianmu_custom_ad_content"
        android:maxLines="2"
        android:layout_below="@+id/tianmu_fl_container"/>
    <TextView
        android:textSize="12sp"
        android:ellipsize="end"
        android:id="@+id/tianmu_tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="20dp"
        android:text="@string/tianmu_custom_ad_title"
        android:maxLines="1"
        android:layout_toLeftOf="@+id/tianmu_tv_action"
        android:layout_below="@+id/tianmu_tv_desc"/>
    <TextView
        android:textSize="12sp"
        android:id="@+id/tianmu_tv_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="32dp"
        android:text="@string/tianmu_custom_ad_check_details"
        android:layout_toLeftOf="@+id/tianmu_iv_close"
        android:layout_alignBottom="@+id/tianmu_tv_title"/>
    <ImageView
        android:id="@+id/tianmu_iv_close"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/tianmu_icon_rec_round_close"
        android:scaleType="centerCrop"
        android:layout_alignBottom="@+id/tianmu_tv_title"
        android:layout_alignParentRight="true"/>
</RelativeLayout>
