<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tianmu_rl_ad_container"
    android:paddingLeft="12dp"
    android:paddingTop="12dp"
    android:paddingRight="12dp"
    android:paddingBottom="12dp"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginBottom="0.5dp">
    <FrameLayout
        android:id="@+id/tianmu_fl_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"/>
    <FrameLayout
        android:id="@+id/tianmu_fl_interaction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/tianmu_fl_container"
        android:layout_alignTop="@+id/tianmu_fl_container"
        android:layout_alignRight="@+id/tianmu_fl_container"
        android:layout_alignBottom="@+id/tianmu_fl_container"/>
    <TextView
        android:id="@+id/tianmu_tv_ad_target"
        android:layout_alignBottom="@+id/tianmu_fl_interaction"
        android:layout_alignParentRight="true"
        style="@style/tianmu_ad_target_base_style"/>
    <TextView
        android:id="@+id/tianmu_banner_tv_ad_source"
        android:layout_alignLeft="@+id/tianmu_fl_container"
        android:layout_alignBottom="@+id/tianmu_fl_container"
        style="@style/tianmu_ad_source_base_style"/>
    <ImageView
        android:id="@+id/tianmu_iv_video_mute"
        android:src="@drawable/tianmu_reward_voice"
        android:layout_alignLeft="@+id/tianmu_fl_container"
        android:layout_alignBottom="@+id/tianmu_fl_container"
        style="@style/tianmu_iv_video_mute_style"/>
    <ImageView
        android:id="@+id/tianmu_iv_close"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/tianmu_icon_round_close"
        android:scaleType="centerCrop"
        android:layout_alignParentRight="true"/>
</RelativeLayout>
