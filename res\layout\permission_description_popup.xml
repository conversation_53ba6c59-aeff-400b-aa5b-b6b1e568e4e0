<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:orientation="vertical"
        android:background="@drawable/permission_description_popup_bg"
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="10dp">
        <TextView
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="12dp"
            android:layout_marginRight="15dp"
            android:text="@string/common_permission_description_title"/>
        <TextView
            android:textSize="13sp"
            android:textColor="#666666"
            android:id="@+id/tv_permission_description_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="15dp"
            android:layout_marginBottom="12dp"
            android:lineSpacingExtra="3dp"/>
    </LinearLayout>
</FrameLayout>
