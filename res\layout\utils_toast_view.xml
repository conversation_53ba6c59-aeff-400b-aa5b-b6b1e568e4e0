<?xml version="1.0" encoding="utf-8"?>
<view xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@drawable/utils_toast_bg"
    android:paddingLeft="16dp"
    android:paddingTop="12dp"
    android:paddingRight="16dp"
    android:paddingBottom="12dp"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    class="com.blankj.utilcode.util.ToastUtils$UtilsMaxWidthRelativeLayout">
    <View
        android:id="@+id/utvLeftIconView"
        android:visibility="gone"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginRight="8dp"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"/>
    <View
        android:id="@+id/utvTopIconView"
        android:visibility="gone"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginBottom="4dp"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"/>
    <View
        android:id="@+id/utvRightIconView"
        android:visibility="gone"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginLeft="8dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"/>
    <View
        android:id="@+id/utvBottomIconView"
        android:visibility="gone"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginTop="4dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"/>
    <TextView
        android:textSize="14sp"
        android:textColor="#de000000"
        android:gravity="center"
        android:id="@android:id/message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toLeftOf="@+id/utvRightIconView"
        android:layout_toRightOf="@+id/utvLeftIconView"
        android:layout_above="@+id/utvBottomIconView"
        android:layout_below="@+id/utvTopIconView"
        android:layout_centerInParent="true"
        android:lineSpacingExtra="2dp"
        android:fontFamily="sans-serif"/>
</view>
