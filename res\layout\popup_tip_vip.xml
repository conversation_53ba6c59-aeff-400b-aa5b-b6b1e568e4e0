<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_gravity="center"
    android:id="@+id/ll_remain_num"
    android:layout_width="match_parent"
    android:layout_height="500dp"
    android:layout_marginLeft="30dp"
    android:layout_marginRight="30dp"
    android:layout_marginHorizontal="30dp">
    <ImageView
        android:id="@+id/iv_close"
        android:visibility="invisible"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginTop="6dp"
        android:src="@mipmap/close2"
        android:layout_marginEnd="2dp"
        android:layout_alignParentEnd="true"/>
    <androidx.appcompat.widget.LinearLayoutCompat
        android:gravity="center"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">
        <androidx.appcompat.widget.LinearLayoutCompat
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:id="@+id/vipBgBox"
            android:background="@drawable/v1"
            android:paddingBottom="24dp"
            android:layout_width="match_parent"
            android:layout_height="340dp"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="0dp"
            android:layout_marginRight="20dp"
            android:layout_marginHorizontal="20dp">
            <androidx.appcompat.widget.LinearLayoutCompat
                android:gravity="center"
                android:orientation="horizontal"
                android:background="@mipmap/bg_remain_time"
                android:visibility="invisible"
                android:layout_width="210dp"
                android:layout_height="70dp"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="30dp"
                android:layout_marginRight="20dp"
                android:layout_marginHorizontal="20dp"/>
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.appcompat.widget.LinearLayoutCompat>
</RelativeLayout>
