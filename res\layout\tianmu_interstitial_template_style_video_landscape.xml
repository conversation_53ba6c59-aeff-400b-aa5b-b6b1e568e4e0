<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/tianmu_interstitial_full_screen_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:id="@+id/tianmu_interstitial_fl_click"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/tianmu_interstitial_container"
        android:layout_alignTop="@+id/tianmu_interstitial_container"
        android:layout_alignRight="@+id/tianmu_interstitial_container"
        android:layout_alignBottom="@+id/tianmu_interstitial_container"/>
    <RelativeLayout
        android:orientation="vertical"
        android:id="@+id/tianmu_interstitial_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">
        <RelativeLayout
            android:id="@+id/tianmu_interstitial_video_container"
            android:background="#000000"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <TextView
                android:textSize="10sp"
                android:textColor="#ffffff"
                android:gravity="center"
                android:id="@+id/tianmu_library_tv_count_down"
                android:background="@drawable/tianmu_shape_75cccccc_circle"
                android:visibility="gone"
                android:layout_width="27.5dp"
                android:layout_height="27.5dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="12dp"
                android:text="--s"
                android:layout_alignParentRight="true"/>
            <ImageView
                android:id="@+id/tianmu_library_iv_mute"
                android:background="@drawable/tianmu_shape_75cccccc_circle"
                android:visibility="gone"
                android:layout_width="27.5dp"
                android:layout_height="27.5dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="12dp"
                android:src="@drawable/tianmu_reward_voice"
                android:scaleType="centerCrop"
                android:layout_below="@+id/tianmu_library_tv_count_down"
                android:layout_alignParentRight="true"/>
            <ImageView
                android:id="@+id/tianmu_interstitial_iv_skip"
                android:background="@drawable/tianmu_shape_75cccccc_circle"
                android:visibility="gone"
                android:layout_width="27.5dp"
                android:layout_height="27.5dp"
                android:layout_marginTop="4dp"
                android:layout_marginRight="5dp"
                android:src="@drawable/tianmu_reward_close"
                android:layout_alignParentRight="true"/>
            <TextView
                android:id="@+id/tianmu_tv_ad_target"
                android:layout_margin="10dp"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                style="@style/tianmu_ad_target_base_style"/>
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/tianmu_rl_ad_interact"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignLeft="@+id/tianmu_interstitial_video_container"
            android:layout_alignTop="@+id/tianmu_interstitial_video_container"
            android:layout_alignRight="@+id/tianmu_interstitial_video_container"
            android:layout_alignBottom="@+id/tianmu_interstitial_video_container"/>
    </RelativeLayout>
</RelativeLayout>
