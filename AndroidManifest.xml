<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:versionCode="1"
    android:versionName="1.0.1"
    android:compileSdkVersion="33"
    android:compileSdkVersionCodename="13"
    package="com.jzjqx.app.qyh"
    platformBuildVersionCode="33"
    platformBuildVersionName="13">
    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="31"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="com.asus.msa.SupplementaryDID.ACCESS"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <permission
        android:name="com.jzjqx.app.qyh.openadsdk.permission.TT_PANGOLIN"
        android:protectionLevel="signature"/>
    <uses-permission android:name="com.jzjqx.app.qyh.openadsdk.permission.TT_PANGOLIN"/>
    <uses-permission android:name="android.permission.GET_TASKS"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES"/>
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN"/>
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW"/>
        </intent>
        <package android:name="com.google.android.gms"/>
        <package android:name="com.android.vending"/>
        <package android:name="com.android.creator"/>
        <package android:name="com.mdid.msa"/>
        <package android:name="com.samsung.android.deviceidservice"/>
        <package android:name="com.coolpad.deviceidsupport"/>
        <package android:name="com.heytap.openid"/>
        <package android:name="com.huawei.hwid"/>
        <package android:name="com.huawei.hwid.tv"/>
        <package android:name="com.huawei.hms"/>
        <package android:name="com.asus.msa.SupplementaryDID"/>
        <package android:name="com.zui.deviceidservice"/>
        <intent>
            <action android:name="android.intent.action.MAIN"/>
            <category android:name="android.intent.category.LAUNCHER"/>
        </intent>
    </queries>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="freemme.permission.msa"/>
    <application
        android:theme="@style/AppTheme"
        android:label="@string/app_name"
        android:icon="@mipmap/logo"
        android:name="com.jzjqx.app.qyh.app.App"
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:supportsRtl="true"
        android:extractNativeLibs="false"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_config"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:requestLegacyExternalStorage="true">
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false"/>
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false"/>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:exported="false"
            android:authorities="com.jzjqx.app.qyh.fileprovider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path"/>
        </provider>
        <provider
            android:name="com.jzjqx.app.qyh.app.XWProvider"
            android:exported="false"
            android:authorities="com.jzjqx.app.qyh.provider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/xw_paths"/>
        </provider>
        <service
            android:name="com.jzjqx.app.qyh.service.DownFileService"
            android:exported="false"/>
        <activity
            android:theme="@style/AppTheme"
            android:label="@string/app_name"
            android:name="com.jzjqx.app.qyh.app.MainActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:label="@string/app_name"
            android:name="com.jzjqx.app.qyh.wxapi.WXEntryActivity"
            android:exported="true"
            android:taskAffinity="com.jzjqx.app.qyh"
            android:launchMode="singleTask"/>
        <activity
            android:theme="@style/QieHZActivityTheme"
            android:name="com.qiehz.taojin.HomeActivity"
            android:exported="false"/>
        <activity
            android:theme="@style/WWAppTheme.NoActionBar"
            android:name="jfq.wowan.com.myapplication.WowanIndex"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:configChanges="smallestScreenSize|screenSize|screenLayout|orientation|keyboardHidden|keyboard"/>
        <activity
            android:theme="@style/WWAppTheme.NoActionBar"
            android:name="jfq.wowan.com.myapplication.DetailActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:configChanges="smallestScreenSize|screenSize|screenLayout|orientation|keyboardHidden|keyboard"/>
        <provider
            android:name="jfq.wowan.com.myapplication.WWProvider"
            android:exported="false"
            android:authorities="com.jzjqx.app.qyh.WWProvider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/ww_paths"/>
        </provider>
        <meta-data
            android:name="android.notch_support"
            android:value="true"/>
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape"/>
        <meta-data
            android:name="ScopedStorage"
            android:value="true"/>
        <activity
            android:theme="@style/Theme.Wall"
            android:name="com.wall.lib_rewardwall.WallActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:configChanges="screenSize|orientation|keyboardHidden"/>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:exported="false"
            android:authorities="com.jzjqx.app.qyh.androidx-startup">
            <meta-data
                android:name="com.zackratos.ultimatebarx.ultimatebarx.UltimateBarXInitializer"
                android:value="androidx.startup"/>
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup"/>
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup"/>
        </provider>
        <provider
            android:name="com.allenliu.versionchecklib.core.VersionFileProvider"
            android:exported="false"
            android:authorities="com.jzjqx.app.qyh.versionProvider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/versionchecklib_file_paths"/>
        </provider>
        <activity
            android:theme="@style/versionCheckLibvtransparentTheme"
            android:name="com.allenliu.versionchecklib.core.JumpActivity"
            android:launchMode="singleTask"/>
        <service
            android:name="com.allenliu.versionchecklib.v2.ui.VersionService"
            android:exported="false"
            android:priority="1000"/>
        <activity
            android:theme="@style/versionCheckLibvtransparentTheme"
            android:name="com.allenliu.versionchecklib.v2.ui.UIActivity"
            android:launchMode="singleTask"/>
        <activity
            android:theme="@style/versionCheckLibvtransparentTheme"
            android:name="com.allenliu.versionchecklib.v2.ui.DownloadingActivity"
            android:launchMode="singleTask"/>
        <activity
            android:theme="@style/versionCheckLibvtransparentTheme"
            android:name="com.allenliu.versionchecklib.v2.ui.DownloadFailedActivity"
            android:launchMode="singleTask"/>
        <activity
            android:theme="@style/ActivityTranslucent"
            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
            android:exported="false"
            android:configChanges="screenSize|orientation|keyboardHidden"
            android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity
            android:theme="@style/ActivityTranslucent"
            android:name="com.blankj.utilcode.util.UtilsTransActivity"
            android:exported="false"
            android:multiprocess="true"
            android:configChanges="screenSize|orientation|keyboardHidden"
            android:windowSoftInputMode="stateAlwaysHidden"/>
        <provider
            android:name="com.blankj.utilcode.util.UtilsFileProvider"
            android:exported="false"
            android:authorities="com.jzjqx.app.qyh.utilcode.fileprovider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/util_code_provider_paths"/>
        </provider>
        <service
            android:name="com.blankj.utilcode.util.MessengerUtils.ServerService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.jzjqx.app.qyh.messenger"/>
            </intent-filter>
        </service>
        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService.SharedMainProcessService"/>
        <service
            android:name="com.liulishuo.filedownloader.services.FileDownloadService.SeparateProcessService"
            android:process=":filedownloader"/>
        <provider
            android:name="razerdp.basepopup.BasePopupInitializer"
            android:exported="false"
            android:multiprocess="true"
            android:authorities="com.github.razerdp.com.jzjqx.app.qyh.BasePopupInitializer"/>
        <activity
            android:theme="@style/tianmu_ad_detail_activity"
            android:name="com.tianmu.biz.activity.AdDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@style/tianmu_translucent_activity"
            android:name="com.tianmu.biz.activity.AdDownloadDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@style/tianmu_ad_detail_activity"
            android:name="com.tianmu.biz.activity.LandscapeAdDetailActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"/>
        <activity
            android:theme="@style/tianmu_translucent_activity"
            android:name="com.tianmu.biz.activity.LandscapeAdDownloadDetailActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"/>
        <activity
            android:name="com.tianmu.biz.activity.AppPermissionsActivity"
            android:exported="false"/>
        <activity
            android:name="com.tianmu.biz.activity.WebViewActivity"
            android:exported="false"/>
        <activity
            android:theme="@style/tianmu_video_no_status"
            android:name="com.tianmu.biz.activity.RewardVodActivity"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@style/tianmu_video_no_status"
            android:name="com.tianmu.biz.activity.FullScreenVodActivity"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@style/tianmu_video_no_status"
            android:name="com.tianmu.biz.activity.LandscapeFullScreenVodActivity"
            android:exported="false"
            android:screenOrientation="landscape"/>
        <activity
            android:theme="@style/tianmu_interstitial_activity"
            android:name="com.tianmu.biz.activity.InterstitialActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:configChanges="screenSize|orientation|keyboardHidden|keyboard"/>
        <activity
            android:theme="@style/tianmu_interstitial_activity"
            android:name="com.tianmu.biz.activity.LandscapeInterstitialActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:configChanges="screenSize|orientation|keyboardHidden|keyboard"/>
        <activity
            android:name="com.tianmu.biz.activity.DownloadListActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>
        <provider
            android:name="com.tianmu.provider.AdFileProvider"
            android:exported="false"
            android:authorities="com.jzjqx.app.qyh.tianmu.fileprovider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/tianmu_file_paths"/>
        </provider>
        <service
            android:name="com.tianmu.biz.download.service.DownloadNoticeService"
            android:exported="false"/>
        <provider
            android:name="com.liulishuo.okdownload.OkDownloadProvider"
            android:exported="false"
            android:authorities="com.jzjqx.app.qyh.com.liulishuo.okdownload"/>
        <meta-data
            android:name="com.huawei.hms.client.service.name:ads-identifier"
            android:value="ads-identifier:3.4.62.300"/>
    </application>
</manifest>
