<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="center"
    android:orientation="vertical"
    android:background="#000"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <FrameLayout
            android:id="@+id/tianmu_fl_canvas"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        <RelativeLayout
            android:id="@+id/tianmu_rl_animal"
            android:visibility="gone"
            android:layout_width="150dp"
            android:layout_height="146dp"
            android:layout_centerInParent="true">
            <ImageView
                android:background="@drawable/tianmu_erase_path"
                android:layout_width="wrap_content"
                android:layout_height="56dp"
                android:layout_centerHorizontal="true"/>
            <ImageView
                android:id="@+id/tianmu_iv_finger"
                android:background="@drawable/tianmu_erase_finger"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:textSize="12sp"
                android:textColor="#fff"
                android:id="@+id/tianmu_tv_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"/>
        </RelativeLayout>
    </RelativeLayout>
</merge>
