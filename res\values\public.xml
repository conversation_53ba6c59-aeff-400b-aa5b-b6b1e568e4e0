<?xml version="1.0" encoding="utf-8"?>
<resources>
    <public type="anim" name="abc_fade_in" id="0x7f010000" />
    <public type="anim" name="abc_fade_out" id="0x7f010001" />
    <public type="anim" name="abc_grow_fade_in_from_bottom" id="0x7f010002" />
    <public type="anim" name="abc_popup_enter" id="0x7f010003" />
    <public type="anim" name="abc_popup_exit" id="0x7f010004" />
    <public type="anim" name="abc_shrink_fade_out_from_bottom" id="0x7f010005" />
    <public type="anim" name="abc_slide_in_bottom" id="0x7f010006" />
    <public type="anim" name="abc_slide_in_top" id="0x7f010007" />
    <public type="anim" name="abc_slide_out_bottom" id="0x7f010008" />
    <public type="anim" name="abc_slide_out_top" id="0x7f010009" />
    <public type="anim" name="abc_tooltip_enter" id="0x7f01000a" />
    <public type="anim" name="abc_tooltip_exit" id="0x7f01000b" />
    <public type="anim" name="btn_checkbox_to_checked_box_inner_merged_animation" id="0x7f01000c" />
    <public type="anim" name="btn_checkbox_to_checked_box_outer_merged_animation" id="0x7f01000d" />
    <public type="anim" name="btn_checkbox_to_checked_icon_null_animation" id="0x7f01000e" />
    <public type="anim" name="btn_checkbox_to_unchecked_box_inner_merged_animation" id="0x7f01000f" />
    <public type="anim" name="btn_checkbox_to_unchecked_check_path_merged_animation" id="0x7f010010" />
    <public type="anim" name="btn_checkbox_to_unchecked_icon_null_animation" id="0x7f010011" />
    <public type="anim" name="btn_radio_to_off_mtrl_dot_group_animation" id="0x7f010012" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_animation" id="0x7f010013" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_path_animation" id="0x7f010014" />
    <public type="anim" name="btn_radio_to_on_mtrl_dot_group_animation" id="0x7f010015" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_animation" id="0x7f010016" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_path_animation" id="0x7f010017" />
    <public type="anim" name="design_bottom_sheet_slide_in" id="0x7f010018" />
    <public type="anim" name="design_bottom_sheet_slide_out" id="0x7f010019" />
    <public type="anim" name="design_snackbar_in" id="0x7f01001a" />
    <public type="anim" name="design_snackbar_out" id="0x7f01001b" />
    <public type="anim" name="fragment_fast_out_extra_slow_in" id="0x7f01001c" />
    <public type="anim" name="mtrl_bottom_sheet_slide_in" id="0x7f01001d" />
    <public type="anim" name="mtrl_bottom_sheet_slide_out" id="0x7f01001e" />
    <public type="anim" name="mtrl_card_lowers_interpolator" id="0x7f01001f" />
    <public type="anim" name="tianmu_alpha_enter" id="0x7f010020" />
    <public type="anim" name="tianmu_alpha_exit" id="0x7f010021" />
    <public type="anim" name="tianmu_download_confirm_dialog_slide_right_in" id="0x7f010022" />
    <public type="anim" name="tianmu_download_confirm_dialog_slide_up" id="0x7f010023" />
    <public type="anim" name="ww_push_left_in" id="0x7f010024" />
    <public type="anim" name="ww_push_left_out" id="0x7f010025" />
    <public type="anim" name="ww_push_right_in" id="0x7f010026" />
    <public type="anim" name="ww_push_right_out" id="0x7f010027" />
    <public type="animator" name="design_appbar_state_list_animator" id="0x7f020000" />
    <public type="animator" name="design_fab_hide_motion_spec" id="0x7f020001" />
    <public type="animator" name="design_fab_show_motion_spec" id="0x7f020002" />
    <public type="animator" name="fragment_close_enter" id="0x7f020003" />
    <public type="animator" name="fragment_close_exit" id="0x7f020004" />
    <public type="animator" name="fragment_fade_enter" id="0x7f020005" />
    <public type="animator" name="fragment_fade_exit" id="0x7f020006" />
    <public type="animator" name="fragment_open_enter" id="0x7f020007" />
    <public type="animator" name="fragment_open_exit" id="0x7f020008" />
    <public type="animator" name="mtrl_btn_state_list_anim" id="0x7f020009" />
    <public type="animator" name="mtrl_btn_unelevated_state_list_anim" id="0x7f02000a" />
    <public type="animator" name="mtrl_card_state_list_anim" id="0x7f02000b" />
    <public type="animator" name="mtrl_chip_state_list_anim" id="0x7f02000c" />
    <public type="animator" name="mtrl_extended_fab_change_size_motion_spec" id="0x7f02000d" />
    <public type="animator" name="mtrl_extended_fab_hide_motion_spec" id="0x7f02000e" />
    <public type="animator" name="mtrl_extended_fab_show_motion_spec" id="0x7f02000f" />
    <public type="animator" name="mtrl_extended_fab_state_list_animator" id="0x7f020010" />
    <public type="animator" name="mtrl_fab_hide_motion_spec" id="0x7f020011" />
    <public type="animator" name="mtrl_fab_show_motion_spec" id="0x7f020012" />
    <public type="animator" name="mtrl_fab_transformation_sheet_collapse_spec" id="0x7f020013" />
    <public type="animator" name="mtrl_fab_transformation_sheet_expand_spec" id="0x7f020014" />
    <public type="attr" name="actionBarDivider" id="0x7f030000" />
    <public type="attr" name="actionBarItemBackground" id="0x7f030001" />
    <public type="attr" name="actionBarPopupTheme" id="0x7f030002" />
    <public type="attr" name="actionBarSize" id="0x7f030003" />
    <public type="attr" name="actionBarSplitStyle" id="0x7f030004" />
    <public type="attr" name="actionBarStyle" id="0x7f030005" />
    <public type="attr" name="actionBarTabBarStyle" id="0x7f030006" />
    <public type="attr" name="actionBarTabStyle" id="0x7f030007" />
    <public type="attr" name="actionBarTabTextStyle" id="0x7f030008" />
    <public type="attr" name="actionBarTheme" id="0x7f030009" />
    <public type="attr" name="actionBarWidgetTheme" id="0x7f03000a" />
    <public type="attr" name="actionButtonStyle" id="0x7f03000b" />
    <public type="attr" name="actionDropDownStyle" id="0x7f03000c" />
    <public type="attr" name="actionLayout" id="0x7f03000d" />
    <public type="attr" name="actionMenuTextAppearance" id="0x7f03000e" />
    <public type="attr" name="actionMenuTextColor" id="0x7f03000f" />
    <public type="attr" name="actionModeBackground" id="0x7f030010" />
    <public type="attr" name="actionModeCloseButtonStyle" id="0x7f030011" />
    <public type="attr" name="actionModeCloseContentDescription" id="0x7f030012" />
    <public type="attr" name="actionModeCloseDrawable" id="0x7f030013" />
    <public type="attr" name="actionModeCopyDrawable" id="0x7f030014" />
    <public type="attr" name="actionModeCutDrawable" id="0x7f030015" />
    <public type="attr" name="actionModeFindDrawable" id="0x7f030016" />
    <public type="attr" name="actionModePasteDrawable" id="0x7f030017" />
    <public type="attr" name="actionModePopupWindowStyle" id="0x7f030018" />
    <public type="attr" name="actionModeSelectAllDrawable" id="0x7f030019" />
    <public type="attr" name="actionModeShareDrawable" id="0x7f03001a" />
    <public type="attr" name="actionModeSplitBackground" id="0x7f03001b" />
    <public type="attr" name="actionModeStyle" id="0x7f03001c" />
    <public type="attr" name="actionModeTheme" id="0x7f03001d" />
    <public type="attr" name="actionModeWebSearchDrawable" id="0x7f03001e" />
    <public type="attr" name="actionOverflowButtonStyle" id="0x7f03001f" />
    <public type="attr" name="actionOverflowMenuStyle" id="0x7f030020" />
    <public type="attr" name="actionProviderClass" id="0x7f030021" />
    <public type="attr" name="actionTextColorAlpha" id="0x7f030022" />
    <public type="attr" name="actionViewClass" id="0x7f030023" />
    <public type="attr" name="activityChooserViewStyle" id="0x7f030024" />
    <public type="attr" name="alertDialogButtonGroupStyle" id="0x7f030025" />
    <public type="attr" name="alertDialogCenterButtons" id="0x7f030026" />
    <public type="attr" name="alertDialogStyle" id="0x7f030027" />
    <public type="attr" name="alertDialogTheme" id="0x7f030028" />
    <public type="attr" name="allowStacking" id="0x7f030029" />
    <public type="attr" name="alpha" id="0x7f03002a" />
    <public type="attr" name="alphabeticModifiers" id="0x7f03002b" />
    <public type="attr" name="animationMode" id="0x7f03002c" />
    <public type="attr" name="appBarLayoutStyle" id="0x7f03002d" />
    <public type="attr" name="arrowHeadLength" id="0x7f03002e" />
    <public type="attr" name="arrowShaftLength" id="0x7f03002f" />
    <public type="attr" name="autoCompleteTextViewStyle" id="0x7f030030" />
    <public type="attr" name="autoSizeMaxTextSize" id="0x7f030031" />
    <public type="attr" name="autoSizeMinTextSize" id="0x7f030032" />
    <public type="attr" name="autoSizePresetSizes" id="0x7f030033" />
    <public type="attr" name="autoSizeStepGranularity" id="0x7f030034" />
    <public type="attr" name="autoSizeTextType" id="0x7f030035" />
    <public type="attr" name="background" id="0x7f030036" />
    <public type="attr" name="backgroundColor" id="0x7f030037" />
    <public type="attr" name="backgroundInsetBottom" id="0x7f030038" />
    <public type="attr" name="backgroundInsetEnd" id="0x7f030039" />
    <public type="attr" name="backgroundInsetStart" id="0x7f03003a" />
    <public type="attr" name="backgroundInsetTop" id="0x7f03003b" />
    <public type="attr" name="backgroundOverlayColorAlpha" id="0x7f03003c" />
    <public type="attr" name="backgroundSplit" id="0x7f03003d" />
    <public type="attr" name="backgroundStacked" id="0x7f03003e" />
    <public type="attr" name="backgroundTint" id="0x7f03003f" />
    <public type="attr" name="backgroundTintMode" id="0x7f030040" />
    <public type="attr" name="badgeGravity" id="0x7f030041" />
    <public type="attr" name="badgeStyle" id="0x7f030042" />
    <public type="attr" name="badgeTextColor" id="0x7f030043" />
    <public type="attr" name="barLength" id="0x7f030044" />
    <public type="attr" name="barrierAllowsGoneWidgets" id="0x7f030045" />
    <public type="attr" name="barrierDirection" id="0x7f030046" />
    <public type="attr" name="behavior_autoHide" id="0x7f030047" />
    <public type="attr" name="behavior_autoShrink" id="0x7f030048" />
    <public type="attr" name="behavior_draggable" id="0x7f030049" />
    <public type="attr" name="behavior_expandedOffset" id="0x7f03004a" />
    <public type="attr" name="behavior_fitToContents" id="0x7f03004b" />
    <public type="attr" name="behavior_halfExpandedRatio" id="0x7f03004c" />
    <public type="attr" name="behavior_hideable" id="0x7f03004d" />
    <public type="attr" name="behavior_overlapTop" id="0x7f03004e" />
    <public type="attr" name="behavior_peekHeight" id="0x7f03004f" />
    <public type="attr" name="behavior_saveFlags" id="0x7f030050" />
    <public type="attr" name="behavior_skipCollapsed" id="0x7f030051" />
    <public type="attr" name="borderWidth" id="0x7f030052" />
    <public type="attr" name="borderlessButtonStyle" id="0x7f030053" />
    <public type="attr" name="bottomAppBarStyle" id="0x7f030054" />
    <public type="attr" name="bottomNavigationStyle" id="0x7f030055" />
    <public type="attr" name="bottomSheetDialogTheme" id="0x7f030056" />
    <public type="attr" name="bottomSheetStyle" id="0x7f030057" />
    <public type="attr" name="boxBackgroundColor" id="0x7f030058" />
    <public type="attr" name="boxBackgroundMode" id="0x7f030059" />
    <public type="attr" name="boxCollapsedPaddingTop" id="0x7f03005a" />
    <public type="attr" name="boxCornerRadiusBottomEnd" id="0x7f03005b" />
    <public type="attr" name="boxCornerRadiusBottomStart" id="0x7f03005c" />
    <public type="attr" name="boxCornerRadiusTopEnd" id="0x7f03005d" />
    <public type="attr" name="boxCornerRadiusTopStart" id="0x7f03005e" />
    <public type="attr" name="boxStrokeColor" id="0x7f03005f" />
    <public type="attr" name="boxStrokeErrorColor" id="0x7f030060" />
    <public type="attr" name="boxStrokeWidth" id="0x7f030061" />
    <public type="attr" name="boxStrokeWidthFocused" id="0x7f030062" />
    <public type="attr" name="buttonBarButtonStyle" id="0x7f030063" />
    <public type="attr" name="buttonBarNegativeButtonStyle" id="0x7f030064" />
    <public type="attr" name="buttonBarNeutralButtonStyle" id="0x7f030065" />
    <public type="attr" name="buttonBarPositiveButtonStyle" id="0x7f030066" />
    <public type="attr" name="buttonBarStyle" id="0x7f030067" />
    <public type="attr" name="buttonCompat" id="0x7f030068" />
    <public type="attr" name="buttonGravity" id="0x7f030069" />
    <public type="attr" name="buttonIconDimen" id="0x7f03006a" />
    <public type="attr" name="buttonPanelSideLayout" id="0x7f03006b" />
    <public type="attr" name="buttonStyle" id="0x7f03006c" />
    <public type="attr" name="buttonStyleSmall" id="0x7f03006d" />
    <public type="attr" name="buttonTint" id="0x7f03006e" />
    <public type="attr" name="buttonTintMode" id="0x7f03006f" />
    <public type="attr" name="cardBackgroundColor" id="0x7f030070" />
    <public type="attr" name="cardCornerRadius" id="0x7f030071" />
    <public type="attr" name="cardElevation" id="0x7f030072" />
    <public type="attr" name="cardForegroundColor" id="0x7f030073" />
    <public type="attr" name="cardMaxElevation" id="0x7f030074" />
    <public type="attr" name="cardPreventCornerOverlap" id="0x7f030075" />
    <public type="attr" name="cardUseCompatPadding" id="0x7f030076" />
    <public type="attr" name="cardViewStyle" id="0x7f030077" />
    <public type="attr" name="chainUseRtl" id="0x7f030078" />
    <public type="attr" name="checkMarkCompat" id="0x7f030079" />
    <public type="attr" name="checkMarkTint" id="0x7f03007a" />
    <public type="attr" name="checkMarkTintMode" id="0x7f03007b" />
    <public type="attr" name="checkboxStyle" id="0x7f03007c" />
    <public type="attr" name="checkedButton" id="0x7f03007d" />
    <public type="attr" name="checkedChip" id="0x7f03007e" />
    <public type="attr" name="checkedIcon" id="0x7f03007f" />
    <public type="attr" name="checkedIconEnabled" id="0x7f030080" />
    <public type="attr" name="checkedIconTint" id="0x7f030081" />
    <public type="attr" name="checkedIconVisible" id="0x7f030082" />
    <public type="attr" name="checkedTextViewStyle" id="0x7f030083" />
    <public type="attr" name="chipBackgroundColor" id="0x7f030084" />
    <public type="attr" name="chipCornerRadius" id="0x7f030085" />
    <public type="attr" name="chipEndPadding" id="0x7f030086" />
    <public type="attr" name="chipGroupStyle" id="0x7f030087" />
    <public type="attr" name="chipIcon" id="0x7f030088" />
    <public type="attr" name="chipIconEnabled" id="0x7f030089" />
    <public type="attr" name="chipIconSize" id="0x7f03008a" />
    <public type="attr" name="chipIconTint" id="0x7f03008b" />
    <public type="attr" name="chipIconVisible" id="0x7f03008c" />
    <public type="attr" name="chipMinHeight" id="0x7f03008d" />
    <public type="attr" name="chipMinTouchTargetSize" id="0x7f03008e" />
    <public type="attr" name="chipSpacing" id="0x7f03008f" />
    <public type="attr" name="chipSpacingHorizontal" id="0x7f030090" />
    <public type="attr" name="chipSpacingVertical" id="0x7f030091" />
    <public type="attr" name="chipStandaloneStyle" id="0x7f030092" />
    <public type="attr" name="chipStartPadding" id="0x7f030093" />
    <public type="attr" name="chipStrokeColor" id="0x7f030094" />
    <public type="attr" name="chipStrokeWidth" id="0x7f030095" />
    <public type="attr" name="chipStyle" id="0x7f030096" />
    <public type="attr" name="chipSurfaceColor" id="0x7f030097" />
    <public type="attr" name="closeIcon" id="0x7f030098" />
    <public type="attr" name="closeIconEnabled" id="0x7f030099" />
    <public type="attr" name="closeIconEndPadding" id="0x7f03009a" />
    <public type="attr" name="closeIconSize" id="0x7f03009b" />
    <public type="attr" name="closeIconStartPadding" id="0x7f03009c" />
    <public type="attr" name="closeIconTint" id="0x7f03009d" />
    <public type="attr" name="closeIconVisible" id="0x7f03009e" />
    <public type="attr" name="closeItemLayout" id="0x7f03009f" />
    <public type="attr" name="collapseContentDescription" id="0x7f0300a0" />
    <public type="attr" name="collapseIcon" id="0x7f0300a1" />
    <public type="attr" name="collapsedTitleGravity" id="0x7f0300a2" />
    <public type="attr" name="collapsedTitleTextAppearance" id="0x7f0300a3" />
    <public type="attr" name="color" id="0x7f0300a4" />
    <public type="attr" name="colorAccent" id="0x7f0300a5" />
    <public type="attr" name="colorBackgroundFloating" id="0x7f0300a6" />
    <public type="attr" name="colorButtonNormal" id="0x7f0300a7" />
    <public type="attr" name="colorControlActivated" id="0x7f0300a8" />
    <public type="attr" name="colorControlHighlight" id="0x7f0300a9" />
    <public type="attr" name="colorControlNormal" id="0x7f0300aa" />
    <public type="attr" name="colorError" id="0x7f0300ab" />
    <public type="attr" name="colorOnBackground" id="0x7f0300ac" />
    <public type="attr" name="colorOnError" id="0x7f0300ad" />
    <public type="attr" name="colorOnPrimary" id="0x7f0300ae" />
    <public type="attr" name="colorOnPrimarySurface" id="0x7f0300af" />
    <public type="attr" name="colorOnSecondary" id="0x7f0300b0" />
    <public type="attr" name="colorOnSurface" id="0x7f0300b1" />
    <public type="attr" name="colorPrimary" id="0x7f0300b2" />
    <public type="attr" name="colorPrimaryDark" id="0x7f0300b3" />
    <public type="attr" name="colorPrimarySurface" id="0x7f0300b4" />
    <public type="attr" name="colorPrimaryVariant" id="0x7f0300b5" />
    <public type="attr" name="colorSecondary" id="0x7f0300b6" />
    <public type="attr" name="colorSecondaryVariant" id="0x7f0300b7" />
    <public type="attr" name="colorSurface" id="0x7f0300b8" />
    <public type="attr" name="colorSwitchThumbNormal" id="0x7f0300b9" />
    <public type="attr" name="commitIcon" id="0x7f0300ba" />
    <public type="attr" name="constraintSet" id="0x7f0300bb" />
    <public type="attr" name="constraint_referenced_ids" id="0x7f0300bc" />
    <public type="attr" name="content" id="0x7f0300bd" />
    <public type="attr" name="contentDescription" id="0x7f0300be" />
    <public type="attr" name="contentInsetEnd" id="0x7f0300bf" />
    <public type="attr" name="contentInsetEndWithActions" id="0x7f0300c0" />
    <public type="attr" name="contentInsetLeft" id="0x7f0300c1" />
    <public type="attr" name="contentInsetRight" id="0x7f0300c2" />
    <public type="attr" name="contentInsetStart" id="0x7f0300c3" />
    <public type="attr" name="contentInsetStartWithNavigation" id="0x7f0300c4" />
    <public type="attr" name="contentPadding" id="0x7f0300c5" />
    <public type="attr" name="contentPaddingBottom" id="0x7f0300c6" />
    <public type="attr" name="contentPaddingLeft" id="0x7f0300c7" />
    <public type="attr" name="contentPaddingRight" id="0x7f0300c8" />
    <public type="attr" name="contentPaddingTop" id="0x7f0300c9" />
    <public type="attr" name="contentScrim" id="0x7f0300ca" />
    <public type="attr" name="controlBackground" id="0x7f0300cb" />
    <public type="attr" name="coordinatorLayoutStyle" id="0x7f0300cc" />
    <public type="attr" name="cornerFamily" id="0x7f0300cd" />
    <public type="attr" name="cornerFamilyBottomLeft" id="0x7f0300ce" />
    <public type="attr" name="cornerFamilyBottomRight" id="0x7f0300cf" />
    <public type="attr" name="cornerFamilyTopLeft" id="0x7f0300d0" />
    <public type="attr" name="cornerFamilyTopRight" id="0x7f0300d1" />
    <public type="attr" name="cornerRadius" id="0x7f0300d2" />
    <public type="attr" name="cornerSize" id="0x7f0300d3" />
    <public type="attr" name="cornerSizeBottomLeft" id="0x7f0300d4" />
    <public type="attr" name="cornerSizeBottomRight" id="0x7f0300d5" />
    <public type="attr" name="cornerSizeTopLeft" id="0x7f0300d6" />
    <public type="attr" name="cornerSizeTopRight" id="0x7f0300d7" />
    <public type="attr" name="counterEnabled" id="0x7f0300d8" />
    <public type="attr" name="counterMaxLength" id="0x7f0300d9" />
    <public type="attr" name="counterOverflowTextAppearance" id="0x7f0300da" />
    <public type="attr" name="counterOverflowTextColor" id="0x7f0300db" />
    <public type="attr" name="counterTextAppearance" id="0x7f0300dc" />
    <public type="attr" name="counterTextColor" id="0x7f0300dd" />
    <public type="attr" name="customNavigationLayout" id="0x7f0300de" />
    <public type="attr" name="dayInvalidStyle" id="0x7f0300df" />
    <public type="attr" name="daySelectedStyle" id="0x7f0300e0" />
    <public type="attr" name="dayStyle" id="0x7f0300e1" />
    <public type="attr" name="dayTodayStyle" id="0x7f0300e2" />
    <public type="attr" name="defaultQueryHint" id="0x7f0300e3" />
    <public type="attr" name="dialogCornerRadius" id="0x7f0300e4" />
    <public type="attr" name="dialogPreferredPadding" id="0x7f0300e5" />
    <public type="attr" name="dialogTheme" id="0x7f0300e6" />
    <public type="attr" name="displayOptions" id="0x7f0300e7" />
    <public type="attr" name="divider" id="0x7f0300e8" />
    <public type="attr" name="dividerHorizontal" id="0x7f0300e9" />
    <public type="attr" name="dividerPadding" id="0x7f0300ea" />
    <public type="attr" name="dividerVertical" id="0x7f0300eb" />
    <public type="attr" name="drawableBottomCompat" id="0x7f0300ec" />
    <public type="attr" name="drawableEndCompat" id="0x7f0300ed" />
    <public type="attr" name="drawableLeftCompat" id="0x7f0300ee" />
    <public type="attr" name="drawableRightCompat" id="0x7f0300ef" />
    <public type="attr" name="drawableSize" id="0x7f0300f0" />
    <public type="attr" name="drawableStartCompat" id="0x7f0300f1" />
    <public type="attr" name="drawableTint" id="0x7f0300f2" />
    <public type="attr" name="drawableTintMode" id="0x7f0300f3" />
    <public type="attr" name="drawableTopCompat" id="0x7f0300f4" />
    <public type="attr" name="drawerArrowStyle" id="0x7f0300f5" />
    <public type="attr" name="dropDownListViewStyle" id="0x7f0300f6" />
    <public type="attr" name="dropdownListPreferredItemHeight" id="0x7f0300f7" />
    <public type="attr" name="editTextBackground" id="0x7f0300f8" />
    <public type="attr" name="editTextColor" id="0x7f0300f9" />
    <public type="attr" name="editTextStyle" id="0x7f0300fa" />
    <public type="attr" name="elevation" id="0x7f0300fb" />
    <public type="attr" name="elevationOverlayColor" id="0x7f0300fc" />
    <public type="attr" name="elevationOverlayEnabled" id="0x7f0300fd" />
    <public type="attr" name="emojiCompatEnabled" id="0x7f0300fe" />
    <public type="attr" name="emptyVisibility" id="0x7f0300ff" />
    <public type="attr" name="endIconCheckable" id="0x7f030100" />
    <public type="attr" name="endIconContentDescription" id="0x7f030101" />
    <public type="attr" name="endIconDrawable" id="0x7f030102" />
    <public type="attr" name="endIconMode" id="0x7f030103" />
    <public type="attr" name="endIconTint" id="0x7f030104" />
    <public type="attr" name="endIconTintMode" id="0x7f030105" />
    <public type="attr" name="enforceMaterialTheme" id="0x7f030106" />
    <public type="attr" name="enforceTextAppearance" id="0x7f030107" />
    <public type="attr" name="ensureMinTouchTargetSize" id="0x7f030108" />
    <public type="attr" name="errorContentDescription" id="0x7f030109" />
    <public type="attr" name="errorEnabled" id="0x7f03010a" />
    <public type="attr" name="errorIconDrawable" id="0x7f03010b" />
    <public type="attr" name="errorIconTint" id="0x7f03010c" />
    <public type="attr" name="errorIconTintMode" id="0x7f03010d" />
    <public type="attr" name="errorTextAppearance" id="0x7f03010e" />
    <public type="attr" name="errorTextColor" id="0x7f03010f" />
    <public type="attr" name="expandActivityOverflowButtonDrawable" id="0x7f030110" />
    <public type="attr" name="expanded" id="0x7f030111" />
    <public type="attr" name="expandedTitleGravity" id="0x7f030112" />
    <public type="attr" name="expandedTitleMargin" id="0x7f030113" />
    <public type="attr" name="expandedTitleMarginBottom" id="0x7f030114" />
    <public type="attr" name="expandedTitleMarginEnd" id="0x7f030115" />
    <public type="attr" name="expandedTitleMarginStart" id="0x7f030116" />
    <public type="attr" name="expandedTitleMarginTop" id="0x7f030117" />
    <public type="attr" name="expandedTitleTextAppearance" id="0x7f030118" />
    <public type="attr" name="extendMotionSpec" id="0x7f030119" />
    <public type="attr" name="extendedFloatingActionButtonStyle" id="0x7f03011a" />
    <public type="attr" name="fabAlignmentMode" id="0x7f03011b" />
    <public type="attr" name="fabAnimationMode" id="0x7f03011c" />
    <public type="attr" name="fabCradleMargin" id="0x7f03011d" />
    <public type="attr" name="fabCradleRoundedCornerRadius" id="0x7f03011e" />
    <public type="attr" name="fabCradleVerticalOffset" id="0x7f03011f" />
    <public type="attr" name="fabCustomSize" id="0x7f030120" />
    <public type="attr" name="fabSize" id="0x7f030121" />
    <public type="attr" name="fastScrollEnabled" id="0x7f030122" />
    <public type="attr" name="fastScrollHorizontalThumbDrawable" id="0x7f030123" />
    <public type="attr" name="fastScrollHorizontalTrackDrawable" id="0x7f030124" />
    <public type="attr" name="fastScrollVerticalThumbDrawable" id="0x7f030125" />
    <public type="attr" name="fastScrollVerticalTrackDrawable" id="0x7f030126" />
    <public type="attr" name="firstBaselineToTopHeight" id="0x7f030127" />
    <public type="attr" name="floatingActionButtonStyle" id="0x7f030128" />
    <public type="attr" name="font" id="0x7f030129" />
    <public type="attr" name="fontFamily" id="0x7f03012a" />
    <public type="attr" name="fontProviderAuthority" id="0x7f03012b" />
    <public type="attr" name="fontProviderCerts" id="0x7f03012c" />
    <public type="attr" name="fontProviderFetchStrategy" id="0x7f03012d" />
    <public type="attr" name="fontProviderFetchTimeout" id="0x7f03012e" />
    <public type="attr" name="fontProviderPackage" id="0x7f03012f" />
    <public type="attr" name="fontProviderQuery" id="0x7f030130" />
    <public type="attr" name="fontProviderSystemFontFamily" id="0x7f030131" />
    <public type="attr" name="fontStyle" id="0x7f030132" />
    <public type="attr" name="fontVariationSettings" id="0x7f030133" />
    <public type="attr" name="fontWeight" id="0x7f030134" />
    <public type="attr" name="foregroundInsidePadding" id="0x7f030135" />
    <public type="attr" name="gapBetweenBars" id="0x7f030136" />
    <public type="attr" name="gestureInsetBottomIgnored" id="0x7f030137" />
    <public type="attr" name="goIcon" id="0x7f030138" />
    <public type="attr" name="haloColor" id="0x7f030139" />
    <public type="attr" name="haloRadius" id="0x7f03013a" />
    <public type="attr" name="headerLayout" id="0x7f03013b" />
    <public type="attr" name="height" id="0x7f03013c" />
    <public type="attr" name="helperText" id="0x7f03013d" />
    <public type="attr" name="helperTextEnabled" id="0x7f03013e" />
    <public type="attr" name="helperTextTextAppearance" id="0x7f03013f" />
    <public type="attr" name="helperTextTextColor" id="0x7f030140" />
    <public type="attr" name="hideMotionSpec" id="0x7f030141" />
    <public type="attr" name="hideOnContentScroll" id="0x7f030142" />
    <public type="attr" name="hideOnScroll" id="0x7f030143" />
    <public type="attr" name="hintAnimationEnabled" id="0x7f030144" />
    <public type="attr" name="hintEnabled" id="0x7f030145" />
    <public type="attr" name="hintTextAppearance" id="0x7f030146" />
    <public type="attr" name="hintTextColor" id="0x7f030147" />
    <public type="attr" name="homeAsUpIndicator" id="0x7f030148" />
    <public type="attr" name="homeLayout" id="0x7f030149" />
    <public type="attr" name="horizontalOffset" id="0x7f03014a" />
    <public type="attr" name="hoveredFocusedTranslationZ" id="0x7f03014b" />
    <public type="attr" name="icon" id="0x7f03014c" />
    <public type="attr" name="iconEndPadding" id="0x7f03014d" />
    <public type="attr" name="iconGravity" id="0x7f03014e" />
    <public type="attr" name="iconPadding" id="0x7f03014f" />
    <public type="attr" name="iconSize" id="0x7f030150" />
    <public type="attr" name="iconStartPadding" id="0x7f030151" />
    <public type="attr" name="iconTint" id="0x7f030152" />
    <public type="attr" name="iconTintMode" id="0x7f030153" />
    <public type="attr" name="iconifiedByDefault" id="0x7f030154" />
    <public type="attr" name="imageButtonStyle" id="0x7f030155" />
    <public type="attr" name="indeterminateProgressStyle" id="0x7f030156" />
    <public type="attr" name="initialActivityCount" id="0x7f030157" />
    <public type="attr" name="insetForeground" id="0x7f030158" />
    <public type="attr" name="isLightTheme" id="0x7f030159" />
    <public type="attr" name="isMaterialTheme" id="0x7f03015a" />
    <public type="attr" name="itemBackground" id="0x7f03015b" />
    <public type="attr" name="itemFillColor" id="0x7f03015c" />
    <public type="attr" name="itemHorizontalPadding" id="0x7f03015d" />
    <public type="attr" name="itemHorizontalTranslationEnabled" id="0x7f03015e" />
    <public type="attr" name="itemIconPadding" id="0x7f03015f" />
    <public type="attr" name="itemIconSize" id="0x7f030160" />
    <public type="attr" name="itemIconTint" id="0x7f030161" />
    <public type="attr" name="itemMaxLines" id="0x7f030162" />
    <public type="attr" name="itemPadding" id="0x7f030163" />
    <public type="attr" name="itemRippleColor" id="0x7f030164" />
    <public type="attr" name="itemShapeAppearance" id="0x7f030165" />
    <public type="attr" name="itemShapeAppearanceOverlay" id="0x7f030166" />
    <public type="attr" name="itemShapeFillColor" id="0x7f030167" />
    <public type="attr" name="itemShapeInsetBottom" id="0x7f030168" />
    <public type="attr" name="itemShapeInsetEnd" id="0x7f030169" />
    <public type="attr" name="itemShapeInsetStart" id="0x7f03016a" />
    <public type="attr" name="itemShapeInsetTop" id="0x7f03016b" />
    <public type="attr" name="itemSpacing" id="0x7f03016c" />
    <public type="attr" name="itemStrokeColor" id="0x7f03016d" />
    <public type="attr" name="itemStrokeWidth" id="0x7f03016e" />
    <public type="attr" name="itemTextAppearance" id="0x7f03016f" />
    <public type="attr" name="itemTextAppearanceActive" id="0x7f030170" />
    <public type="attr" name="itemTextAppearanceInactive" id="0x7f030171" />
    <public type="attr" name="itemTextColor" id="0x7f030172" />
    <public type="attr" name="keylines" id="0x7f030173" />
    <public type="attr" name="lStar" id="0x7f030174" />
    <public type="attr" name="labelBehavior" id="0x7f030175" />
    <public type="attr" name="labelStyle" id="0x7f030176" />
    <public type="attr" name="labelVisibilityMode" id="0x7f030177" />
    <public type="attr" name="lastBaselineToBottomHeight" id="0x7f030178" />
    <public type="attr" name="layout" id="0x7f030179" />
    <public type="attr" name="layoutManager" id="0x7f03017a" />
    <public type="attr" name="layout_anchor" id="0x7f03017b" />
    <public type="attr" name="layout_anchorGravity" id="0x7f03017c" />
    <public type="attr" name="layout_behavior" id="0x7f03017d" />
    <public type="attr" name="layout_collapseMode" id="0x7f03017e" />
    <public type="attr" name="layout_collapseParallaxMultiplier" id="0x7f03017f" />
    <public type="attr" name="layout_constrainedHeight" id="0x7f030180" />
    <public type="attr" name="layout_constrainedWidth" id="0x7f030181" />
    <public type="attr" name="layout_constraintBaseline_creator" id="0x7f030182" />
    <public type="attr" name="layout_constraintBaseline_toBaselineOf" id="0x7f030183" />
    <public type="attr" name="layout_constraintBottom_creator" id="0x7f030184" />
    <public type="attr" name="layout_constraintBottom_toBottomOf" id="0x7f030185" />
    <public type="attr" name="layout_constraintBottom_toTopOf" id="0x7f030186" />
    <public type="attr" name="layout_constraintCircle" id="0x7f030187" />
    <public type="attr" name="layout_constraintCircleAngle" id="0x7f030188" />
    <public type="attr" name="layout_constraintCircleRadius" id="0x7f030189" />
    <public type="attr" name="layout_constraintDimensionRatio" id="0x7f03018a" />
    <public type="attr" name="layout_constraintEnd_toEndOf" id="0x7f03018b" />
    <public type="attr" name="layout_constraintEnd_toStartOf" id="0x7f03018c" />
    <public type="attr" name="layout_constraintGuide_begin" id="0x7f03018d" />
    <public type="attr" name="layout_constraintGuide_end" id="0x7f03018e" />
    <public type="attr" name="layout_constraintGuide_percent" id="0x7f03018f" />
    <public type="attr" name="layout_constraintHeight_default" id="0x7f030190" />
    <public type="attr" name="layout_constraintHeight_max" id="0x7f030191" />
    <public type="attr" name="layout_constraintHeight_min" id="0x7f030192" />
    <public type="attr" name="layout_constraintHeight_percent" id="0x7f030193" />
    <public type="attr" name="layout_constraintHorizontal_bias" id="0x7f030194" />
    <public type="attr" name="layout_constraintHorizontal_chainStyle" id="0x7f030195" />
    <public type="attr" name="layout_constraintHorizontal_weight" id="0x7f030196" />
    <public type="attr" name="layout_constraintLeft_creator" id="0x7f030197" />
    <public type="attr" name="layout_constraintLeft_toLeftOf" id="0x7f030198" />
    <public type="attr" name="layout_constraintLeft_toRightOf" id="0x7f030199" />
    <public type="attr" name="layout_constraintRight_creator" id="0x7f03019a" />
    <public type="attr" name="layout_constraintRight_toLeftOf" id="0x7f03019b" />
    <public type="attr" name="layout_constraintRight_toRightOf" id="0x7f03019c" />
    <public type="attr" name="layout_constraintStart_toEndOf" id="0x7f03019d" />
    <public type="attr" name="layout_constraintStart_toStartOf" id="0x7f03019e" />
    <public type="attr" name="layout_constraintTop_creator" id="0x7f03019f" />
    <public type="attr" name="layout_constraintTop_toBottomOf" id="0x7f0301a0" />
    <public type="attr" name="layout_constraintTop_toTopOf" id="0x7f0301a1" />
    <public type="attr" name="layout_constraintVertical_bias" id="0x7f0301a2" />
    <public type="attr" name="layout_constraintVertical_chainStyle" id="0x7f0301a3" />
    <public type="attr" name="layout_constraintVertical_weight" id="0x7f0301a4" />
    <public type="attr" name="layout_constraintWidth_default" id="0x7f0301a5" />
    <public type="attr" name="layout_constraintWidth_max" id="0x7f0301a6" />
    <public type="attr" name="layout_constraintWidth_min" id="0x7f0301a7" />
    <public type="attr" name="layout_constraintWidth_percent" id="0x7f0301a8" />
    <public type="attr" name="layout_dodgeInsetEdges" id="0x7f0301a9" />
    <public type="attr" name="layout_editor_absoluteX" id="0x7f0301aa" />
    <public type="attr" name="layout_editor_absoluteY" id="0x7f0301ab" />
    <public type="attr" name="layout_goneMarginBottom" id="0x7f0301ac" />
    <public type="attr" name="layout_goneMarginEnd" id="0x7f0301ad" />
    <public type="attr" name="layout_goneMarginLeft" id="0x7f0301ae" />
    <public type="attr" name="layout_goneMarginRight" id="0x7f0301af" />
    <public type="attr" name="layout_goneMarginStart" id="0x7f0301b0" />
    <public type="attr" name="layout_goneMarginTop" id="0x7f0301b1" />
    <public type="attr" name="layout_insetEdge" id="0x7f0301b2" />
    <public type="attr" name="layout_keyline" id="0x7f0301b3" />
    <public type="attr" name="layout_optimizationLevel" id="0x7f0301b4" />
    <public type="attr" name="layout_scrollFlags" id="0x7f0301b5" />
    <public type="attr" name="layout_scrollInterpolator" id="0x7f0301b6" />
    <public type="attr" name="liftOnScroll" id="0x7f0301b7" />
    <public type="attr" name="liftOnScrollTargetViewId" id="0x7f0301b8" />
    <public type="attr" name="lineHeight" id="0x7f0301b9" />
    <public type="attr" name="lineSpacing" id="0x7f0301ba" />
    <public type="attr" name="listChoiceBackgroundIndicator" id="0x7f0301bb" />
    <public type="attr" name="listChoiceIndicatorMultipleAnimated" id="0x7f0301bc" />
    <public type="attr" name="listChoiceIndicatorSingleAnimated" id="0x7f0301bd" />
    <public type="attr" name="listDividerAlertDialog" id="0x7f0301be" />
    <public type="attr" name="listItemLayout" id="0x7f0301bf" />
    <public type="attr" name="listLayout" id="0x7f0301c0" />
    <public type="attr" name="listMenuViewStyle" id="0x7f0301c1" />
    <public type="attr" name="listPopupWindowStyle" id="0x7f0301c2" />
    <public type="attr" name="listPreferredItemHeight" id="0x7f0301c3" />
    <public type="attr" name="listPreferredItemHeightLarge" id="0x7f0301c4" />
    <public type="attr" name="listPreferredItemHeightSmall" id="0x7f0301c5" />
    <public type="attr" name="listPreferredItemPaddingEnd" id="0x7f0301c6" />
    <public type="attr" name="listPreferredItemPaddingLeft" id="0x7f0301c7" />
    <public type="attr" name="listPreferredItemPaddingRight" id="0x7f0301c8" />
    <public type="attr" name="listPreferredItemPaddingStart" id="0x7f0301c9" />
    <public type="attr" name="logo" id="0x7f0301ca" />
    <public type="attr" name="logoDescription" id="0x7f0301cb" />
    <public type="attr" name="materialAlertDialogBodyTextStyle" id="0x7f0301cc" />
    <public type="attr" name="materialAlertDialogTheme" id="0x7f0301cd" />
    <public type="attr" name="materialAlertDialogTitleIconStyle" id="0x7f0301ce" />
    <public type="attr" name="materialAlertDialogTitlePanelStyle" id="0x7f0301cf" />
    <public type="attr" name="materialAlertDialogTitleTextStyle" id="0x7f0301d0" />
    <public type="attr" name="materialButtonOutlinedStyle" id="0x7f0301d1" />
    <public type="attr" name="materialButtonStyle" id="0x7f0301d2" />
    <public type="attr" name="materialButtonToggleGroupStyle" id="0x7f0301d3" />
    <public type="attr" name="materialCalendarDay" id="0x7f0301d4" />
    <public type="attr" name="materialCalendarFullscreenTheme" id="0x7f0301d5" />
    <public type="attr" name="materialCalendarHeaderConfirmButton" id="0x7f0301d6" />
    <public type="attr" name="materialCalendarHeaderDivider" id="0x7f0301d7" />
    <public type="attr" name="materialCalendarHeaderLayout" id="0x7f0301d8" />
    <public type="attr" name="materialCalendarHeaderSelection" id="0x7f0301d9" />
    <public type="attr" name="materialCalendarHeaderTitle" id="0x7f0301da" />
    <public type="attr" name="materialCalendarHeaderToggleButton" id="0x7f0301db" />
    <public type="attr" name="materialCalendarStyle" id="0x7f0301dc" />
    <public type="attr" name="materialCalendarTheme" id="0x7f0301dd" />
    <public type="attr" name="materialCardViewStyle" id="0x7f0301de" />
    <public type="attr" name="materialThemeOverlay" id="0x7f0301df" />
    <public type="attr" name="maxActionInlineWidth" id="0x7f0301e0" />
    <public type="attr" name="maxButtonHeight" id="0x7f0301e1" />
    <public type="attr" name="maxCharacterCount" id="0x7f0301e2" />
    <public type="attr" name="maxImageSize" id="0x7f0301e3" />
    <public type="attr" name="maxLines" id="0x7f0301e4" />
    <public type="attr" name="maxProgress" id="0x7f0301e5" />
    <public type="attr" name="measureWithLargestChild" id="0x7f0301e6" />
    <public type="attr" name="menu" id="0x7f0301e7" />
    <public type="attr" name="minTouchTargetSize" id="0x7f0301e8" />
    <public type="attr" name="multiChoiceItemLayout" id="0x7f0301e9" />
    <public type="attr" name="navigationContentDescription" id="0x7f0301ea" />
    <public type="attr" name="navigationIcon" id="0x7f0301eb" />
    <public type="attr" name="navigationMode" id="0x7f0301ec" />
    <public type="attr" name="navigationViewStyle" id="0x7f0301ed" />
    <public type="attr" name="nestedScrollViewStyle" id="0x7f0301ee" />
    <public type="attr" name="number" id="0x7f0301ef" />
    <public type="attr" name="numericModifiers" id="0x7f0301f0" />
    <public type="attr" name="overlapAnchor" id="0x7f0301f1" />
    <public type="attr" name="paddingBottomNoButtons" id="0x7f0301f2" />
    <public type="attr" name="paddingBottomSystemWindowInsets" id="0x7f0301f3" />
    <public type="attr" name="paddingEnd" id="0x7f0301f4" />
    <public type="attr" name="paddingLeftSystemWindowInsets" id="0x7f0301f5" />
    <public type="attr" name="paddingRightSystemWindowInsets" id="0x7f0301f6" />
    <public type="attr" name="paddingStart" id="0x7f0301f7" />
    <public type="attr" name="paddingTopNoTitle" id="0x7f0301f8" />
    <public type="attr" name="panelBackground" id="0x7f0301f9" />
    <public type="attr" name="panelMenuListTheme" id="0x7f0301fa" />
    <public type="attr" name="panelMenuListWidth" id="0x7f0301fb" />
    <public type="attr" name="passwordToggleContentDescription" id="0x7f0301fc" />
    <public type="attr" name="passwordToggleDrawable" id="0x7f0301fd" />
    <public type="attr" name="passwordToggleEnabled" id="0x7f0301fe" />
    <public type="attr" name="passwordToggleTint" id="0x7f0301ff" />
    <public type="attr" name="passwordToggleTintMode" id="0x7f030200" />
    <public type="attr" name="placeholderText" id="0x7f030201" />
    <public type="attr" name="placeholderTextAppearance" id="0x7f030202" />
    <public type="attr" name="placeholderTextColor" id="0x7f030203" />
    <public type="attr" name="popupMenuBackground" id="0x7f030204" />
    <public type="attr" name="popupMenuStyle" id="0x7f030205" />
    <public type="attr" name="popupTheme" id="0x7f030206" />
    <public type="attr" name="popupWindowStyle" id="0x7f030207" />
    <public type="attr" name="prefixText" id="0x7f030208" />
    <public type="attr" name="prefixTextAppearance" id="0x7f030209" />
    <public type="attr" name="prefixTextColor" id="0x7f03020a" />
    <public type="attr" name="preserveIconSpacing" id="0x7f03020b" />
    <public type="attr" name="pressedTranslationZ" id="0x7f03020c" />
    <public type="attr" name="progress" id="0x7f03020d" />
    <public type="attr" name="progressBarPadding" id="0x7f03020e" />
    <public type="attr" name="progressBarStyle" id="0x7f03020f" />
    <public type="attr" name="progressbarBackgroundColor" id="0x7f030210" />
    <public type="attr" name="progressbarColor" id="0x7f030211" />
    <public type="attr" name="queryBackground" id="0x7f030212" />
    <public type="attr" name="queryHint" id="0x7f030213" />
    <public type="attr" name="queryPatterns" id="0x7f030214" />
    <public type="attr" name="radioButtonStyle" id="0x7f030215" />
    <public type="attr" name="radius" id="0x7f030216" />
    <public type="attr" name="rangeFillColor" id="0x7f030217" />
    <public type="attr" name="ratingBarStyle" id="0x7f030218" />
    <public type="attr" name="ratingBarStyleIndicator" id="0x7f030219" />
    <public type="attr" name="ratingBarStyleSmall" id="0x7f03021a" />
    <public type="attr" name="recyclerViewStyle" id="0x7f03021b" />
    <public type="attr" name="reverseLayout" id="0x7f03021c" />
    <public type="attr" name="rippleColor" id="0x7f03021d" />
    <public type="attr" name="scrimAnimationDuration" id="0x7f03021e" />
    <public type="attr" name="scrimBackground" id="0x7f03021f" />
    <public type="attr" name="scrimVisibleHeightTrigger" id="0x7f030220" />
    <public type="attr" name="searchHintIcon" id="0x7f030221" />
    <public type="attr" name="searchIcon" id="0x7f030222" />
    <public type="attr" name="searchViewStyle" id="0x7f030223" />
    <public type="attr" name="seekBarStyle" id="0x7f030224" />
    <public type="attr" name="selectableItemBackground" id="0x7f030225" />
    <public type="attr" name="selectableItemBackgroundBorderless" id="0x7f030226" />
    <public type="attr" name="selectionRequired" id="0x7f030227" />
    <public type="attr" name="shapeAppearance" id="0x7f030228" />
    <public type="attr" name="shapeAppearanceLargeComponent" id="0x7f030229" />
    <public type="attr" name="shapeAppearanceMediumComponent" id="0x7f03022a" />
    <public type="attr" name="shapeAppearanceOverlay" id="0x7f03022b" />
    <public type="attr" name="shapeAppearanceSmallComponent" id="0x7f03022c" />
    <public type="attr" name="shortcutMatchRequired" id="0x7f03022d" />
    <public type="attr" name="showAsAction" id="0x7f03022e" />
    <public type="attr" name="showDividers" id="0x7f03022f" />
    <public type="attr" name="showMotionSpec" id="0x7f030230" />
    <public type="attr" name="showText" id="0x7f030231" />
    <public type="attr" name="showTitle" id="0x7f030232" />
    <public type="attr" name="shrinkMotionSpec" id="0x7f030233" />
    <public type="attr" name="singleChoiceItemLayout" id="0x7f030234" />
    <public type="attr" name="singleLine" id="0x7f030235" />
    <public type="attr" name="singleSelection" id="0x7f030236" />
    <public type="attr" name="sliderStyle" id="0x7f030237" />
    <public type="attr" name="snackbarButtonStyle" id="0x7f030238" />
    <public type="attr" name="snackbarStyle" id="0x7f030239" />
    <public type="attr" name="snackbarTextViewStyle" id="0x7f03023a" />
    <public type="attr" name="spanCount" id="0x7f03023b" />
    <public type="attr" name="spinBars" id="0x7f03023c" />
    <public type="attr" name="spinnerDropDownItemStyle" id="0x7f03023d" />
    <public type="attr" name="spinnerStyle" id="0x7f03023e" />
    <public type="attr" name="splitTrack" id="0x7f03023f" />
    <public type="attr" name="srcCompat" id="0x7f030240" />
    <public type="attr" name="stackFromEnd" id="0x7f030241" />
    <public type="attr" name="startIconCheckable" id="0x7f030242" />
    <public type="attr" name="startIconContentDescription" id="0x7f030243" />
    <public type="attr" name="startIconDrawable" id="0x7f030244" />
    <public type="attr" name="startIconTint" id="0x7f030245" />
    <public type="attr" name="startIconTintMode" id="0x7f030246" />
    <public type="attr" name="state_above_anchor" id="0x7f030247" />
    <public type="attr" name="state_collapsed" id="0x7f030248" />
    <public type="attr" name="state_collapsible" id="0x7f030249" />
    <public type="attr" name="state_dragged" id="0x7f03024a" />
    <public type="attr" name="state_liftable" id="0x7f03024b" />
    <public type="attr" name="state_lifted" id="0x7f03024c" />
    <public type="attr" name="statusBarBackground" id="0x7f03024d" />
    <public type="attr" name="statusBarForeground" id="0x7f03024e" />
    <public type="attr" name="statusBarScrim" id="0x7f03024f" />
    <public type="attr" name="strokeColor" id="0x7f030250" />
    <public type="attr" name="strokeWidth" id="0x7f030251" />
    <public type="attr" name="subMenuArrow" id="0x7f030252" />
    <public type="attr" name="submitBackground" id="0x7f030253" />
    <public type="attr" name="subtitle" id="0x7f030254" />
    <public type="attr" name="subtitleTextAppearance" id="0x7f030255" />
    <public type="attr" name="subtitleTextColor" id="0x7f030256" />
    <public type="attr" name="subtitleTextStyle" id="0x7f030257" />
    <public type="attr" name="suffixText" id="0x7f030258" />
    <public type="attr" name="suffixTextAppearance" id="0x7f030259" />
    <public type="attr" name="suffixTextColor" id="0x7f03025a" />
    <public type="attr" name="suggestionRowLayout" id="0x7f03025b" />
    <public type="attr" name="switchMinWidth" id="0x7f03025c" />
    <public type="attr" name="switchPadding" id="0x7f03025d" />
    <public type="attr" name="switchStyle" id="0x7f03025e" />
    <public type="attr" name="switchTextAppearance" id="0x7f03025f" />
    <public type="attr" name="tabBackground" id="0x7f030260" />
    <public type="attr" name="tabContentStart" id="0x7f030261" />
    <public type="attr" name="tabGravity" id="0x7f030262" />
    <public type="attr" name="tabIconTint" id="0x7f030263" />
    <public type="attr" name="tabIconTintMode" id="0x7f030264" />
    <public type="attr" name="tabIndicator" id="0x7f030265" />
    <public type="attr" name="tabIndicatorAnimationDuration" id="0x7f030266" />
    <public type="attr" name="tabIndicatorColor" id="0x7f030267" />
    <public type="attr" name="tabIndicatorFullWidth" id="0x7f030268" />
    <public type="attr" name="tabIndicatorGravity" id="0x7f030269" />
    <public type="attr" name="tabIndicatorHeight" id="0x7f03026a" />
    <public type="attr" name="tabInlineLabel" id="0x7f03026b" />
    <public type="attr" name="tabMaxWidth" id="0x7f03026c" />
    <public type="attr" name="tabMinWidth" id="0x7f03026d" />
    <public type="attr" name="tabMode" id="0x7f03026e" />
    <public type="attr" name="tabPadding" id="0x7f03026f" />
    <public type="attr" name="tabPaddingBottom" id="0x7f030270" />
    <public type="attr" name="tabPaddingEnd" id="0x7f030271" />
    <public type="attr" name="tabPaddingStart" id="0x7f030272" />
    <public type="attr" name="tabPaddingTop" id="0x7f030273" />
    <public type="attr" name="tabRippleColor" id="0x7f030274" />
    <public type="attr" name="tabSelectedTextColor" id="0x7f030275" />
    <public type="attr" name="tabStyle" id="0x7f030276" />
    <public type="attr" name="tabTextAppearance" id="0x7f030277" />
    <public type="attr" name="tabTextColor" id="0x7f030278" />
    <public type="attr" name="tabUnboundedRipple" id="0x7f030279" />
    <public type="attr" name="text" id="0x7f03027a" />
    <public type="attr" name="textAllCaps" id="0x7f03027b" />
    <public type="attr" name="textAppearanceBody1" id="0x7f03027c" />
    <public type="attr" name="textAppearanceBody2" id="0x7f03027d" />
    <public type="attr" name="textAppearanceButton" id="0x7f03027e" />
    <public type="attr" name="textAppearanceCaption" id="0x7f03027f" />
    <public type="attr" name="textAppearanceHeadline1" id="0x7f030280" />
    <public type="attr" name="textAppearanceHeadline2" id="0x7f030281" />
    <public type="attr" name="textAppearanceHeadline3" id="0x7f030282" />
    <public type="attr" name="textAppearanceHeadline4" id="0x7f030283" />
    <public type="attr" name="textAppearanceHeadline5" id="0x7f030284" />
    <public type="attr" name="textAppearanceHeadline6" id="0x7f030285" />
    <public type="attr" name="textAppearanceLargePopupMenu" id="0x7f030286" />
    <public type="attr" name="textAppearanceLineHeightEnabled" id="0x7f030287" />
    <public type="attr" name="textAppearanceListItem" id="0x7f030288" />
    <public type="attr" name="textAppearanceListItemSecondary" id="0x7f030289" />
    <public type="attr" name="textAppearanceListItemSmall" id="0x7f03028a" />
    <public type="attr" name="textAppearanceOverline" id="0x7f03028b" />
    <public type="attr" name="textAppearancePopupMenuHeader" id="0x7f03028c" />
    <public type="attr" name="textAppearanceSearchResultSubtitle" id="0x7f03028d" />
    <public type="attr" name="textAppearanceSearchResultTitle" id="0x7f03028e" />
    <public type="attr" name="textAppearanceSmallPopupMenu" id="0x7f03028f" />
    <public type="attr" name="textAppearanceSubtitle1" id="0x7f030290" />
    <public type="attr" name="textAppearanceSubtitle2" id="0x7f030291" />
    <public type="attr" name="textColor" id="0x7f030292" />
    <public type="attr" name="textColorAlertDialogListItem" id="0x7f030293" />
    <public type="attr" name="textColorSearchUrl" id="0x7f030294" />
    <public type="attr" name="textEndPadding" id="0x7f030295" />
    <public type="attr" name="textInputLayoutFocusedRectEnabled" id="0x7f030296" />
    <public type="attr" name="textInputStyle" id="0x7f030297" />
    <public type="attr" name="textLocale" id="0x7f030298" />
    <public type="attr" name="textSize" id="0x7f030299" />
    <public type="attr" name="textStartPadding" id="0x7f03029a" />
    <public type="attr" name="theme" id="0x7f03029b" />
    <public type="attr" name="themeLineHeight" id="0x7f03029c" />
    <public type="attr" name="thickness" id="0x7f03029d" />
    <public type="attr" name="thumbColor" id="0x7f03029e" />
    <public type="attr" name="thumbElevation" id="0x7f03029f" />
    <public type="attr" name="thumbRadius" id="0x7f0302a0" />
    <public type="attr" name="thumbTextPadding" id="0x7f0302a1" />
    <public type="attr" name="thumbTint" id="0x7f0302a2" />
    <public type="attr" name="thumbTintMode" id="0x7f0302a3" />
    <public type="attr" name="tianmu_enableAudioFocus" id="0x7f0302a4" />
    <public type="attr" name="tianmu_looping" id="0x7f0302a5" />
    <public type="attr" name="tianmu_playerBackgroundColor" id="0x7f0302a6" />
    <public type="attr" name="tianmu_radius" id="0x7f0302a7" />
    <public type="attr" name="tianmu_screenScaleType" id="0x7f0302a8" />
    <public type="attr" name="tianmu_shimmer_auto_start" id="0x7f0302a9" />
    <public type="attr" name="tianmu_shimmer_base_alpha" id="0x7f0302aa" />
    <public type="attr" name="tianmu_shimmer_base_color" id="0x7f0302ab" />
    <public type="attr" name="tianmu_shimmer_clip_to_children" id="0x7f0302ac" />
    <public type="attr" name="tianmu_shimmer_colored" id="0x7f0302ad" />
    <public type="attr" name="tianmu_shimmer_direction" id="0x7f0302ae" />
    <public type="attr" name="tianmu_shimmer_dropoff" id="0x7f0302af" />
    <public type="attr" name="tianmu_shimmer_duration" id="0x7f0302b0" />
    <public type="attr" name="tianmu_shimmer_fixed_height" id="0x7f0302b1" />
    <public type="attr" name="tianmu_shimmer_fixed_width" id="0x7f0302b2" />
    <public type="attr" name="tianmu_shimmer_height_ratio" id="0x7f0302b3" />
    <public type="attr" name="tianmu_shimmer_highlight_alpha" id="0x7f0302b4" />
    <public type="attr" name="tianmu_shimmer_highlight_color" id="0x7f0302b5" />
    <public type="attr" name="tianmu_shimmer_intensity" id="0x7f0302b6" />
    <public type="attr" name="tianmu_shimmer_repeat_count" id="0x7f0302b7" />
    <public type="attr" name="tianmu_shimmer_repeat_delay" id="0x7f0302b8" />
    <public type="attr" name="tianmu_shimmer_repeat_mode" id="0x7f0302b9" />
    <public type="attr" name="tianmu_shimmer_shape" id="0x7f0302ba" />
    <public type="attr" name="tianmu_shimmer_tilt" id="0x7f0302bb" />
    <public type="attr" name="tianmu_shimmer_width_ratio" id="0x7f0302bc" />
    <public type="attr" name="tickColor" id="0x7f0302bd" />
    <public type="attr" name="tickColorActive" id="0x7f0302be" />
    <public type="attr" name="tickColorInactive" id="0x7f0302bf" />
    <public type="attr" name="tickMark" id="0x7f0302c0" />
    <public type="attr" name="tickMarkTint" id="0x7f0302c1" />
    <public type="attr" name="tickMarkTintMode" id="0x7f0302c2" />
    <public type="attr" name="tint" id="0x7f0302c3" />
    <public type="attr" name="tintMode" id="0x7f0302c4" />
    <public type="attr" name="title" id="0x7f0302c5" />
    <public type="attr" name="titleEnabled" id="0x7f0302c6" />
    <public type="attr" name="titleMargin" id="0x7f0302c7" />
    <public type="attr" name="titleMarginBottom" id="0x7f0302c8" />
    <public type="attr" name="titleMarginEnd" id="0x7f0302c9" />
    <public type="attr" name="titleMarginStart" id="0x7f0302ca" />
    <public type="attr" name="titleMarginTop" id="0x7f0302cb" />
    <public type="attr" name="titleMargins" id="0x7f0302cc" />
    <public type="attr" name="titleTextAppearance" id="0x7f0302cd" />
    <public type="attr" name="titleTextColor" id="0x7f0302ce" />
    <public type="attr" name="titleTextStyle" id="0x7f0302cf" />
    <public type="attr" name="toolbarId" id="0x7f0302d0" />
    <public type="attr" name="toolbarNavigationButtonStyle" id="0x7f0302d1" />
    <public type="attr" name="toolbarStyle" id="0x7f0302d2" />
    <public type="attr" name="tooltipForegroundColor" id="0x7f0302d3" />
    <public type="attr" name="tooltipFrameBackground" id="0x7f0302d4" />
    <public type="attr" name="tooltipStyle" id="0x7f0302d5" />
    <public type="attr" name="tooltipText" id="0x7f0302d6" />
    <public type="attr" name="track" id="0x7f0302d7" />
    <public type="attr" name="trackColor" id="0x7f0302d8" />
    <public type="attr" name="trackColorActive" id="0x7f0302d9" />
    <public type="attr" name="trackColorInactive" id="0x7f0302da" />
    <public type="attr" name="trackHeight" id="0x7f0302db" />
    <public type="attr" name="trackTint" id="0x7f0302dc" />
    <public type="attr" name="trackTintMode" id="0x7f0302dd" />
    <public type="attr" name="transitionShapeAppearance" id="0x7f0302de" />
    <public type="attr" name="ttcIndex" id="0x7f0302df" />
    <public type="attr" name="useCompatPadding" id="0x7f0302e0" />
    <public type="attr" name="useMaterialThemeColors" id="0x7f0302e1" />
    <public type="attr" name="values" id="0x7f0302e2" />
    <public type="attr" name="verticalOffset" id="0x7f0302e3" />
    <public type="attr" name="viewInflaterClass" id="0x7f0302e4" />
    <public type="attr" name="voiceIcon" id="0x7f0302e5" />
    <public type="attr" name="windowActionBar" id="0x7f0302e6" />
    <public type="attr" name="windowActionBarOverlay" id="0x7f0302e7" />
    <public type="attr" name="windowActionModeOverlay" id="0x7f0302e8" />
    <public type="attr" name="windowFixedHeightMajor" id="0x7f0302e9" />
    <public type="attr" name="windowFixedHeightMinor" id="0x7f0302ea" />
    <public type="attr" name="windowFixedWidthMajor" id="0x7f0302eb" />
    <public type="attr" name="windowFixedWidthMinor" id="0x7f0302ec" />
    <public type="attr" name="windowMinWidthMajor" id="0x7f0302ed" />
    <public type="attr" name="windowMinWidthMinor" id="0x7f0302ee" />
    <public type="attr" name="windowNoTitle" id="0x7f0302ef" />
    <public type="attr" name="yearSelectedStyle" id="0x7f0302f0" />
    <public type="attr" name="yearStyle" id="0x7f0302f1" />
    <public type="attr" name="yearTodayStyle" id="0x7f0302f2" />
    <public type="bool" name="abc_action_bar_embed_tabs" id="0x7f040000" />
    <public type="bool" name="abc_config_actionMenuItemAllCaps" id="0x7f040001" />
    <public type="bool" name="mtrl_btn_textappearance_all_caps" id="0x7f040002" />
    <public type="color" name="abc_background_cache_hint_selector_material_dark" id="0x7f050000" />
    <public type="color" name="abc_background_cache_hint_selector_material_light" id="0x7f050001" />
    <public type="color" name="abc_btn_colored_borderless_text_material" id="0x7f050002" />
    <public type="color" name="abc_btn_colored_text_material" id="0x7f050003" />
    <public type="color" name="abc_color_highlight_material" id="0x7f050004" />
    <public type="color" name="abc_decor_view_status_guard" id="0x7f050005" />
    <public type="color" name="abc_decor_view_status_guard_light" id="0x7f050006" />
    <public type="color" name="abc_hint_foreground_material_dark" id="0x7f050007" />
    <public type="color" name="abc_hint_foreground_material_light" id="0x7f050008" />
    <public type="color" name="abc_primary_text_disable_only_material_dark" id="0x7f050009" />
    <public type="color" name="abc_primary_text_disable_only_material_light" id="0x7f05000a" />
    <public type="color" name="abc_primary_text_material_dark" id="0x7f05000b" />
    <public type="color" name="abc_primary_text_material_light" id="0x7f05000c" />
    <public type="color" name="abc_search_url_text" id="0x7f05000d" />
    <public type="color" name="abc_search_url_text_normal" id="0x7f05000e" />
    <public type="color" name="abc_search_url_text_pressed" id="0x7f05000f" />
    <public type="color" name="abc_search_url_text_selected" id="0x7f050010" />
    <public type="color" name="abc_secondary_text_material_dark" id="0x7f050011" />
    <public type="color" name="abc_secondary_text_material_light" id="0x7f050012" />
    <public type="color" name="abc_tint_btn_checkable" id="0x7f050013" />
    <public type="color" name="abc_tint_default" id="0x7f050014" />
    <public type="color" name="abc_tint_edittext" id="0x7f050015" />
    <public type="color" name="abc_tint_seek_thumb" id="0x7f050016" />
    <public type="color" name="abc_tint_spinner" id="0x7f050017" />
    <public type="color" name="abc_tint_switch_track" id="0x7f050018" />
    <public type="color" name="accent_material_dark" id="0x7f050019" />
    <public type="color" name="accent_material_light" id="0x7f05001a" />
    <public type="color" name="androidx_core_ripple_material_light" id="0x7f05001b" />
    <public type="color" name="androidx_core_secondary_text_default_material_light" id="0x7f05001c" />
    <public type="color" name="background_floating_material_dark" id="0x7f05001d" />
    <public type="color" name="background_floating_material_light" id="0x7f05001e" />
    <public type="color" name="background_material_dark" id="0x7f05001f" />
    <public type="color" name="background_material_light" id="0x7f050020" />
    <public type="color" name="black" id="0x7f050021" />
    <public type="color" name="bright_foreground_disabled_material_dark" id="0x7f050022" />
    <public type="color" name="bright_foreground_disabled_material_light" id="0x7f050023" />
    <public type="color" name="bright_foreground_inverse_material_dark" id="0x7f050024" />
    <public type="color" name="bright_foreground_inverse_material_light" id="0x7f050025" />
    <public type="color" name="bright_foreground_material_dark" id="0x7f050026" />
    <public type="color" name="bright_foreground_material_light" id="0x7f050027" />
    <public type="color" name="button_material_dark" id="0x7f050028" />
    <public type="color" name="button_material_light" id="0x7f050029" />
    <public type="color" name="cardview_dark_background" id="0x7f05002a" />
    <public type="color" name="cardview_light_background" id="0x7f05002b" />
    <public type="color" name="cardview_shadow_end_color" id="0x7f05002c" />
    <public type="color" name="cardview_shadow_start_color" id="0x7f05002d" />
    <public type="color" name="checkbox_themeable_attribute_color" id="0x7f05002e" />
    <public type="color" name="colorAccent" id="0x7f05002f" />
    <public type="color" name="colorPrimary" id="0x7f050030" />
    <public type="color" name="colorPrimaryDark" id="0x7f050031" />
    <public type="color" name="color_af1611" id="0x7f050032" />
    <public type="color" name="color_ff0000" id="0x7f050033" />
    <public type="color" name="color_ffe1a0" id="0x7f050034" />
    <public type="color" name="color_refresh" id="0x7f050035" />
    <public type="color" name="design_bottom_navigation_shadow_color" id="0x7f050036" />
    <public type="color" name="design_box_stroke_color" id="0x7f050037" />
    <public type="color" name="design_dark_default_color_background" id="0x7f050038" />
    <public type="color" name="design_dark_default_color_error" id="0x7f050039" />
    <public type="color" name="design_dark_default_color_on_background" id="0x7f05003a" />
    <public type="color" name="design_dark_default_color_on_error" id="0x7f05003b" />
    <public type="color" name="design_dark_default_color_on_primary" id="0x7f05003c" />
    <public type="color" name="design_dark_default_color_on_secondary" id="0x7f05003d" />
    <public type="color" name="design_dark_default_color_on_surface" id="0x7f05003e" />
    <public type="color" name="design_dark_default_color_primary" id="0x7f05003f" />
    <public type="color" name="design_dark_default_color_primary_dark" id="0x7f050040" />
    <public type="color" name="design_dark_default_color_primary_variant" id="0x7f050041" />
    <public type="color" name="design_dark_default_color_secondary" id="0x7f050042" />
    <public type="color" name="design_dark_default_color_secondary_variant" id="0x7f050043" />
    <public type="color" name="design_dark_default_color_surface" id="0x7f050044" />
    <public type="color" name="design_default_color_background" id="0x7f050045" />
    <public type="color" name="design_default_color_error" id="0x7f050046" />
    <public type="color" name="design_default_color_on_background" id="0x7f050047" />
    <public type="color" name="design_default_color_on_error" id="0x7f050048" />
    <public type="color" name="design_default_color_on_primary" id="0x7f050049" />
    <public type="color" name="design_default_color_on_secondary" id="0x7f05004a" />
    <public type="color" name="design_default_color_on_surface" id="0x7f05004b" />
    <public type="color" name="design_default_color_primary" id="0x7f05004c" />
    <public type="color" name="design_default_color_primary_dark" id="0x7f05004d" />
    <public type="color" name="design_default_color_primary_variant" id="0x7f05004e" />
    <public type="color" name="design_default_color_secondary" id="0x7f05004f" />
    <public type="color" name="design_default_color_secondary_variant" id="0x7f050050" />
    <public type="color" name="design_default_color_surface" id="0x7f050051" />
    <public type="color" name="design_error" id="0x7f050052" />
    <public type="color" name="design_fab_shadow_end_color" id="0x7f050053" />
    <public type="color" name="design_fab_shadow_mid_color" id="0x7f050054" />
    <public type="color" name="design_fab_shadow_start_color" id="0x7f050055" />
    <public type="color" name="design_fab_stroke_end_inner_color" id="0x7f050056" />
    <public type="color" name="design_fab_stroke_end_outer_color" id="0x7f050057" />
    <public type="color" name="design_fab_stroke_top_inner_color" id="0x7f050058" />
    <public type="color" name="design_fab_stroke_top_outer_color" id="0x7f050059" />
    <public type="color" name="design_icon_tint" id="0x7f05005a" />
    <public type="color" name="design_snackbar_background_color" id="0x7f05005b" />
    <public type="color" name="dim_foreground_disabled_material_dark" id="0x7f05005c" />
    <public type="color" name="dim_foreground_disabled_material_light" id="0x7f05005d" />
    <public type="color" name="dim_foreground_material_dark" id="0x7f05005e" />
    <public type="color" name="dim_foreground_material_light" id="0x7f05005f" />
    <public type="color" name="error_color_material_dark" id="0x7f050060" />
    <public type="color" name="error_color_material_light" id="0x7f050061" />
    <public type="color" name="foreground_material_dark" id="0x7f050062" />
    <public type="color" name="foreground_material_light" id="0x7f050063" />
    <public type="color" name="highlighted_text_material_dark" id="0x7f050064" />
    <public type="color" name="highlighted_text_material_light" id="0x7f050065" />
    <public type="color" name="material_blue_grey_800" id="0x7f050066" />
    <public type="color" name="material_blue_grey_900" id="0x7f050067" />
    <public type="color" name="material_blue_grey_950" id="0x7f050068" />
    <public type="color" name="material_deep_teal_200" id="0x7f050069" />
    <public type="color" name="material_deep_teal_500" id="0x7f05006a" />
    <public type="color" name="material_grey_100" id="0x7f05006b" />
    <public type="color" name="material_grey_300" id="0x7f05006c" />
    <public type="color" name="material_grey_50" id="0x7f05006d" />
    <public type="color" name="material_grey_600" id="0x7f05006e" />
    <public type="color" name="material_grey_800" id="0x7f05006f" />
    <public type="color" name="material_grey_850" id="0x7f050070" />
    <public type="color" name="material_grey_900" id="0x7f050071" />
    <public type="color" name="material_on_background_disabled" id="0x7f050072" />
    <public type="color" name="material_on_background_emphasis_high_type" id="0x7f050073" />
    <public type="color" name="material_on_background_emphasis_medium" id="0x7f050074" />
    <public type="color" name="material_on_primary_disabled" id="0x7f050075" />
    <public type="color" name="material_on_primary_emphasis_high_type" id="0x7f050076" />
    <public type="color" name="material_on_primary_emphasis_medium" id="0x7f050077" />
    <public type="color" name="material_on_surface_disabled" id="0x7f050078" />
    <public type="color" name="material_on_surface_emphasis_high_type" id="0x7f050079" />
    <public type="color" name="material_on_surface_emphasis_medium" id="0x7f05007a" />
    <public type="color" name="material_on_surface_stroke" id="0x7f05007b" />
    <public type="color" name="material_slider_active_tick_marks_color" id="0x7f05007c" />
    <public type="color" name="material_slider_active_track_color" id="0x7f05007d" />
    <public type="color" name="material_slider_halo_color" id="0x7f05007e" />
    <public type="color" name="material_slider_inactive_tick_marks_color" id="0x7f05007f" />
    <public type="color" name="material_slider_inactive_track_color" id="0x7f050080" />
    <public type="color" name="material_slider_thumb_color" id="0x7f050081" />
    <public type="color" name="mtrl_bottom_nav_colored_item_tint" id="0x7f050082" />
    <public type="color" name="mtrl_bottom_nav_colored_ripple_color" id="0x7f050083" />
    <public type="color" name="mtrl_bottom_nav_item_tint" id="0x7f050084" />
    <public type="color" name="mtrl_bottom_nav_ripple_color" id="0x7f050085" />
    <public type="color" name="mtrl_btn_bg_color_selector" id="0x7f050086" />
    <public type="color" name="mtrl_btn_ripple_color" id="0x7f050087" />
    <public type="color" name="mtrl_btn_stroke_color_selector" id="0x7f050088" />
    <public type="color" name="mtrl_btn_text_btn_bg_color_selector" id="0x7f050089" />
    <public type="color" name="mtrl_btn_text_btn_ripple_color" id="0x7f05008a" />
    <public type="color" name="mtrl_btn_text_color_disabled" id="0x7f05008b" />
    <public type="color" name="mtrl_btn_text_color_selector" id="0x7f05008c" />
    <public type="color" name="mtrl_btn_transparent_bg_color" id="0x7f05008d" />
    <public type="color" name="mtrl_calendar_item_stroke_color" id="0x7f05008e" />
    <public type="color" name="mtrl_calendar_selected_range" id="0x7f05008f" />
    <public type="color" name="mtrl_card_view_foreground" id="0x7f050090" />
    <public type="color" name="mtrl_card_view_ripple" id="0x7f050091" />
    <public type="color" name="mtrl_chip_background_color" id="0x7f050092" />
    <public type="color" name="mtrl_chip_close_icon_tint" id="0x7f050093" />
    <public type="color" name="mtrl_chip_ripple_color" id="0x7f050094" />
    <public type="color" name="mtrl_chip_surface_color" id="0x7f050095" />
    <public type="color" name="mtrl_chip_text_color" id="0x7f050096" />
    <public type="color" name="mtrl_choice_chip_background_color" id="0x7f050097" />
    <public type="color" name="mtrl_choice_chip_ripple_color" id="0x7f050098" />
    <public type="color" name="mtrl_choice_chip_text_color" id="0x7f050099" />
    <public type="color" name="mtrl_error" id="0x7f05009a" />
    <public type="color" name="mtrl_fab_bg_color_selector" id="0x7f05009b" />
    <public type="color" name="mtrl_fab_icon_text_color_selector" id="0x7f05009c" />
    <public type="color" name="mtrl_fab_ripple_color" id="0x7f05009d" />
    <public type="color" name="mtrl_filled_background_color" id="0x7f05009e" />
    <public type="color" name="mtrl_filled_icon_tint" id="0x7f05009f" />
    <public type="color" name="mtrl_filled_stroke_color" id="0x7f0500a0" />
    <public type="color" name="mtrl_indicator_text_color" id="0x7f0500a1" />
    <public type="color" name="mtrl_navigation_item_background_color" id="0x7f0500a2" />
    <public type="color" name="mtrl_navigation_item_icon_tint" id="0x7f0500a3" />
    <public type="color" name="mtrl_navigation_item_text_color" id="0x7f0500a4" />
    <public type="color" name="mtrl_on_primary_text_btn_text_color_selector" id="0x7f0500a5" />
    <public type="color" name="mtrl_outlined_icon_tint" id="0x7f0500a6" />
    <public type="color" name="mtrl_outlined_stroke_color" id="0x7f0500a7" />
    <public type="color" name="mtrl_popupmenu_overlay_color" id="0x7f0500a8" />
    <public type="color" name="mtrl_scrim_color" id="0x7f0500a9" />
    <public type="color" name="mtrl_tabs_colored_ripple_color" id="0x7f0500aa" />
    <public type="color" name="mtrl_tabs_icon_color_selector" id="0x7f0500ab" />
    <public type="color" name="mtrl_tabs_icon_color_selector_colored" id="0x7f0500ac" />
    <public type="color" name="mtrl_tabs_legacy_text_color_selector" id="0x7f0500ad" />
    <public type="color" name="mtrl_tabs_ripple_color" id="0x7f0500ae" />
    <public type="color" name="mtrl_text_btn_text_color_selector" id="0x7f0500af" />
    <public type="color" name="mtrl_textinput_default_box_stroke_color" id="0x7f0500b0" />
    <public type="color" name="mtrl_textinput_disabled_color" id="0x7f0500b1" />
    <public type="color" name="mtrl_textinput_filled_box_default_background_color" id="0x7f0500b2" />
    <public type="color" name="mtrl_textinput_focused_box_stroke_color" id="0x7f0500b3" />
    <public type="color" name="mtrl_textinput_hovered_box_stroke_color" id="0x7f0500b4" />
    <public type="color" name="notification_action_color_filter" id="0x7f0500b5" />
    <public type="color" name="notification_icon_bg_color" id="0x7f0500b6" />
    <public type="color" name="notification_material_background_media_default_color" id="0x7f0500b7" />
    <public type="color" name="primary_dark_material_dark" id="0x7f0500b8" />
    <public type="color" name="primary_dark_material_light" id="0x7f0500b9" />
    <public type="color" name="primary_material_dark" id="0x7f0500ba" />
    <public type="color" name="primary_material_light" id="0x7f0500bb" />
    <public type="color" name="primary_text_default_material_dark" id="0x7f0500bc" />
    <public type="color" name="primary_text_default_material_light" id="0x7f0500bd" />
    <public type="color" name="primary_text_disabled_material_dark" id="0x7f0500be" />
    <public type="color" name="primary_text_disabled_material_light" id="0x7f0500bf" />
    <public type="color" name="purple_200" id="0x7f0500c0" />
    <public type="color" name="purple_500" id="0x7f0500c1" />
    <public type="color" name="purple_700" id="0x7f0500c2" />
    <public type="color" name="radiobutton_themeable_attribute_color" id="0x7f0500c3" />
    <public type="color" name="ripple_material_dark" id="0x7f0500c4" />
    <public type="color" name="ripple_material_light" id="0x7f0500c5" />
    <public type="color" name="secondary_text_default_material_dark" id="0x7f0500c6" />
    <public type="color" name="secondary_text_default_material_light" id="0x7f0500c7" />
    <public type="color" name="secondary_text_disabled_material_dark" id="0x7f0500c8" />
    <public type="color" name="secondary_text_disabled_material_light" id="0x7f0500c9" />
    <public type="color" name="switch_thumb_disabled_material_dark" id="0x7f0500ca" />
    <public type="color" name="switch_thumb_disabled_material_light" id="0x7f0500cb" />
    <public type="color" name="switch_thumb_material_dark" id="0x7f0500cc" />
    <public type="color" name="switch_thumb_material_light" id="0x7f0500cd" />
    <public type="color" name="switch_thumb_normal_material_dark" id="0x7f0500ce" />
    <public type="color" name="switch_thumb_normal_material_light" id="0x7f0500cf" />
    <public type="color" name="teal_200" id="0x7f0500d0" />
    <public type="color" name="teal_700" id="0x7f0500d1" />
    <public type="color" name="test_mtrl_calendar_day" id="0x7f0500d2" />
    <public type="color" name="test_mtrl_calendar_day_selected" id="0x7f0500d3" />
    <public type="color" name="tianmu_splash_download_info_color" id="0x7f0500d4" />
    <public type="color" name="tianmu_splash_title_color1" id="0x7f0500d5" />
    <public type="color" name="tianmu_splash_title_color2" id="0x7f0500d6" />
    <public type="color" name="tianmu_splash_title_color3" id="0x7f0500d7" />
    <public type="color" name="tianmu_video_background_color" id="0x7f0500d8" />
    <public type="color" name="tianmu_video_theme_color" id="0x7f0500d9" />
    <public type="color" name="tianmu_video_theme_color_translucent" id="0x7f0500da" />
    <public type="color" name="title_color" id="0x7f0500db" />
    <public type="color" name="tooltip_background_dark" id="0x7f0500dc" />
    <public type="color" name="tooltip_background_light" id="0x7f0500dd" />
    <public type="color" name="versionchecklib_theme_color" id="0x7f0500de" />
    <public type="color" name="white" id="0x7f0500df" />
    <public type="dimen" name="abc_action_bar_content_inset_material" id="0x7f060000" />
    <public type="dimen" name="abc_action_bar_content_inset_with_nav" id="0x7f060001" />
    <public type="dimen" name="abc_action_bar_default_height_material" id="0x7f060002" />
    <public type="dimen" name="abc_action_bar_default_padding_end_material" id="0x7f060003" />
    <public type="dimen" name="abc_action_bar_default_padding_start_material" id="0x7f060004" />
    <public type="dimen" name="abc_action_bar_elevation_material" id="0x7f060005" />
    <public type="dimen" name="abc_action_bar_icon_vertical_padding_material" id="0x7f060006" />
    <public type="dimen" name="abc_action_bar_overflow_padding_end_material" id="0x7f060007" />
    <public type="dimen" name="abc_action_bar_overflow_padding_start_material" id="0x7f060008" />
    <public type="dimen" name="abc_action_bar_stacked_max_height" id="0x7f060009" />
    <public type="dimen" name="abc_action_bar_stacked_tab_max_width" id="0x7f06000a" />
    <public type="dimen" name="abc_action_bar_subtitle_bottom_margin_material" id="0x7f06000b" />
    <public type="dimen" name="abc_action_bar_subtitle_top_margin_material" id="0x7f06000c" />
    <public type="dimen" name="abc_action_button_min_height_material" id="0x7f06000d" />
    <public type="dimen" name="abc_action_button_min_width_material" id="0x7f06000e" />
    <public type="dimen" name="abc_action_button_min_width_overflow_material" id="0x7f06000f" />
    <public type="dimen" name="abc_alert_dialog_button_bar_height" id="0x7f060010" />
    <public type="dimen" name="abc_alert_dialog_button_dimen" id="0x7f060011" />
    <public type="dimen" name="abc_button_inset_horizontal_material" id="0x7f060012" />
    <public type="dimen" name="abc_button_inset_vertical_material" id="0x7f060013" />
    <public type="dimen" name="abc_button_padding_horizontal_material" id="0x7f060014" />
    <public type="dimen" name="abc_button_padding_vertical_material" id="0x7f060015" />
    <public type="dimen" name="abc_cascading_menus_min_smallest_width" id="0x7f060016" />
    <public type="dimen" name="abc_config_prefDialogWidth" id="0x7f060017" />
    <public type="dimen" name="abc_control_corner_material" id="0x7f060018" />
    <public type="dimen" name="abc_control_inset_material" id="0x7f060019" />
    <public type="dimen" name="abc_control_padding_material" id="0x7f06001a" />
    <public type="dimen" name="abc_dialog_corner_radius_material" id="0x7f06001b" />
    <public type="dimen" name="abc_dialog_fixed_height_major" id="0x7f06001c" />
    <public type="dimen" name="abc_dialog_fixed_height_minor" id="0x7f06001d" />
    <public type="dimen" name="abc_dialog_fixed_width_major" id="0x7f06001e" />
    <public type="dimen" name="abc_dialog_fixed_width_minor" id="0x7f06001f" />
    <public type="dimen" name="abc_dialog_list_padding_bottom_no_buttons" id="0x7f060020" />
    <public type="dimen" name="abc_dialog_list_padding_top_no_title" id="0x7f060021" />
    <public type="dimen" name="abc_dialog_min_width_major" id="0x7f060022" />
    <public type="dimen" name="abc_dialog_min_width_minor" id="0x7f060023" />
    <public type="dimen" name="abc_dialog_padding_material" id="0x7f060024" />
    <public type="dimen" name="abc_dialog_padding_top_material" id="0x7f060025" />
    <public type="dimen" name="abc_dialog_title_divider_material" id="0x7f060026" />
    <public type="dimen" name="abc_disabled_alpha_material_dark" id="0x7f060027" />
    <public type="dimen" name="abc_disabled_alpha_material_light" id="0x7f060028" />
    <public type="dimen" name="abc_dropdownitem_icon_width" id="0x7f060029" />
    <public type="dimen" name="abc_dropdownitem_text_padding_left" id="0x7f06002a" />
    <public type="dimen" name="abc_dropdownitem_text_padding_right" id="0x7f06002b" />
    <public type="dimen" name="abc_edit_text_inset_bottom_material" id="0x7f06002c" />
    <public type="dimen" name="abc_edit_text_inset_horizontal_material" id="0x7f06002d" />
    <public type="dimen" name="abc_edit_text_inset_top_material" id="0x7f06002e" />
    <public type="dimen" name="abc_floating_window_z" id="0x7f06002f" />
    <public type="dimen" name="abc_list_item_height_large_material" id="0x7f060030" />
    <public type="dimen" name="abc_list_item_height_material" id="0x7f060031" />
    <public type="dimen" name="abc_list_item_height_small_material" id="0x7f060032" />
    <public type="dimen" name="abc_list_item_padding_horizontal_material" id="0x7f060033" />
    <public type="dimen" name="abc_panel_menu_list_width" id="0x7f060034" />
    <public type="dimen" name="abc_progress_bar_height_material" id="0x7f060035" />
    <public type="dimen" name="abc_search_view_preferred_height" id="0x7f060036" />
    <public type="dimen" name="abc_search_view_preferred_width" id="0x7f060037" />
    <public type="dimen" name="abc_seekbar_track_background_height_material" id="0x7f060038" />
    <public type="dimen" name="abc_seekbar_track_progress_height_material" id="0x7f060039" />
    <public type="dimen" name="abc_select_dialog_padding_start_material" id="0x7f06003a" />
    <public type="dimen" name="abc_star_big" id="0x7f06003b" />
    <public type="dimen" name="abc_star_medium" id="0x7f06003c" />
    <public type="dimen" name="abc_star_small" id="0x7f06003d" />
    <public type="dimen" name="abc_switch_padding" id="0x7f06003e" />
    <public type="dimen" name="abc_text_size_body_1_material" id="0x7f06003f" />
    <public type="dimen" name="abc_text_size_body_2_material" id="0x7f060040" />
    <public type="dimen" name="abc_text_size_button_material" id="0x7f060041" />
    <public type="dimen" name="abc_text_size_caption_material" id="0x7f060042" />
    <public type="dimen" name="abc_text_size_display_1_material" id="0x7f060043" />
    <public type="dimen" name="abc_text_size_display_2_material" id="0x7f060044" />
    <public type="dimen" name="abc_text_size_display_3_material" id="0x7f060045" />
    <public type="dimen" name="abc_text_size_display_4_material" id="0x7f060046" />
    <public type="dimen" name="abc_text_size_headline_material" id="0x7f060047" />
    <public type="dimen" name="abc_text_size_large_material" id="0x7f060048" />
    <public type="dimen" name="abc_text_size_medium_material" id="0x7f060049" />
    <public type="dimen" name="abc_text_size_menu_header_material" id="0x7f06004a" />
    <public type="dimen" name="abc_text_size_menu_material" id="0x7f06004b" />
    <public type="dimen" name="abc_text_size_small_material" id="0x7f06004c" />
    <public type="dimen" name="abc_text_size_subhead_material" id="0x7f06004d" />
    <public type="dimen" name="abc_text_size_subtitle_material_toolbar" id="0x7f06004e" />
    <public type="dimen" name="abc_text_size_title_material" id="0x7f06004f" />
    <public type="dimen" name="abc_text_size_title_material_toolbar" id="0x7f060050" />
    <public type="dimen" name="action_bar_size" id="0x7f060051" />
    <public type="dimen" name="activity_horizontal_margin" id="0x7f060052" />
    <public type="dimen" name="activity_vertical_margin" id="0x7f060053" />
    <public type="dimen" name="appcompat_dialog_background_inset" id="0x7f060054" />
    <public type="dimen" name="cardview_compat_inset_shadow" id="0x7f060055" />
    <public type="dimen" name="cardview_default_elevation" id="0x7f060056" />
    <public type="dimen" name="cardview_default_radius" id="0x7f060057" />
    <public type="dimen" name="compat_button_inset_horizontal_material" id="0x7f060058" />
    <public type="dimen" name="compat_button_inset_vertical_material" id="0x7f060059" />
    <public type="dimen" name="compat_button_padding_horizontal_material" id="0x7f06005a" />
    <public type="dimen" name="compat_button_padding_vertical_material" id="0x7f06005b" />
    <public type="dimen" name="compat_control_corner_material" id="0x7f06005c" />
    <public type="dimen" name="compat_notification_large_icon_max_height" id="0x7f06005d" />
    <public type="dimen" name="compat_notification_large_icon_max_width" id="0x7f06005e" />
    <public type="dimen" name="default_dimension" id="0x7f06005f" />
    <public type="dimen" name="design_appbar_elevation" id="0x7f060060" />
    <public type="dimen" name="design_bottom_navigation_active_item_max_width" id="0x7f060061" />
    <public type="dimen" name="design_bottom_navigation_active_item_min_width" id="0x7f060062" />
    <public type="dimen" name="design_bottom_navigation_active_text_size" id="0x7f060063" />
    <public type="dimen" name="design_bottom_navigation_elevation" id="0x7f060064" />
    <public type="dimen" name="design_bottom_navigation_height" id="0x7f060065" />
    <public type="dimen" name="design_bottom_navigation_icon_size" id="0x7f060066" />
    <public type="dimen" name="design_bottom_navigation_item_max_width" id="0x7f060067" />
    <public type="dimen" name="design_bottom_navigation_item_min_width" id="0x7f060068" />
    <public type="dimen" name="design_bottom_navigation_margin" id="0x7f060069" />
    <public type="dimen" name="design_bottom_navigation_shadow_height" id="0x7f06006a" />
    <public type="dimen" name="design_bottom_navigation_text_size" id="0x7f06006b" />
    <public type="dimen" name="design_bottom_sheet_elevation" id="0x7f06006c" />
    <public type="dimen" name="design_bottom_sheet_modal_elevation" id="0x7f06006d" />
    <public type="dimen" name="design_bottom_sheet_peek_height_min" id="0x7f06006e" />
    <public type="dimen" name="design_fab_border_width" id="0x7f06006f" />
    <public type="dimen" name="design_fab_elevation" id="0x7f060070" />
    <public type="dimen" name="design_fab_image_size" id="0x7f060071" />
    <public type="dimen" name="design_fab_size_mini" id="0x7f060072" />
    <public type="dimen" name="design_fab_size_normal" id="0x7f060073" />
    <public type="dimen" name="design_fab_translation_z_hovered_focused" id="0x7f060074" />
    <public type="dimen" name="design_fab_translation_z_pressed" id="0x7f060075" />
    <public type="dimen" name="design_navigation_elevation" id="0x7f060076" />
    <public type="dimen" name="design_navigation_icon_padding" id="0x7f060077" />
    <public type="dimen" name="design_navigation_icon_size" id="0x7f060078" />
    <public type="dimen" name="design_navigation_item_horizontal_padding" id="0x7f060079" />
    <public type="dimen" name="design_navigation_item_icon_padding" id="0x7f06007a" />
    <public type="dimen" name="design_navigation_max_width" id="0x7f06007b" />
    <public type="dimen" name="design_navigation_padding_bottom" id="0x7f06007c" />
    <public type="dimen" name="design_navigation_separator_vertical_padding" id="0x7f06007d" />
    <public type="dimen" name="design_snackbar_action_inline_max_width" id="0x7f06007e" />
    <public type="dimen" name="design_snackbar_action_text_color_alpha" id="0x7f06007f" />
    <public type="dimen" name="design_snackbar_background_corner_radius" id="0x7f060080" />
    <public type="dimen" name="design_snackbar_elevation" id="0x7f060081" />
    <public type="dimen" name="design_snackbar_extra_spacing_horizontal" id="0x7f060082" />
    <public type="dimen" name="design_snackbar_max_width" id="0x7f060083" />
    <public type="dimen" name="design_snackbar_min_width" id="0x7f060084" />
    <public type="dimen" name="design_snackbar_padding_horizontal" id="0x7f060085" />
    <public type="dimen" name="design_snackbar_padding_vertical" id="0x7f060086" />
    <public type="dimen" name="design_snackbar_padding_vertical_2lines" id="0x7f060087" />
    <public type="dimen" name="design_snackbar_text_size" id="0x7f060088" />
    <public type="dimen" name="design_tab_max_width" id="0x7f060089" />
    <public type="dimen" name="design_tab_scrollable_min_width" id="0x7f06008a" />
    <public type="dimen" name="design_tab_text_size" id="0x7f06008b" />
    <public type="dimen" name="design_tab_text_size_2line" id="0x7f06008c" />
    <public type="dimen" name="design_textinput_caption_translate_y" id="0x7f06008d" />
    <public type="dimen" name="disabled_alpha_material_dark" id="0x7f06008e" />
    <public type="dimen" name="disabled_alpha_material_light" id="0x7f06008f" />
    <public type="dimen" name="fastscroll_default_thickness" id="0x7f060090" />
    <public type="dimen" name="fastscroll_margin" id="0x7f060091" />
    <public type="dimen" name="fastscroll_minimum_range" id="0x7f060092" />
    <public type="dimen" name="highlight_alpha_material_colored" id="0x7f060093" />
    <public type="dimen" name="highlight_alpha_material_dark" id="0x7f060094" />
    <public type="dimen" name="highlight_alpha_material_light" id="0x7f060095" />
    <public type="dimen" name="hint_alpha_material_dark" id="0x7f060096" />
    <public type="dimen" name="hint_alpha_material_light" id="0x7f060097" />
    <public type="dimen" name="hint_pressed_alpha_material_dark" id="0x7f060098" />
    <public type="dimen" name="hint_pressed_alpha_material_light" id="0x7f060099" />
    <public type="dimen" name="item_touch_helper_max_drag_scroll_per_frame" id="0x7f06009a" />
    <public type="dimen" name="item_touch_helper_swipe_escape_max_velocity" id="0x7f06009b" />
    <public type="dimen" name="item_touch_helper_swipe_escape_velocity" id="0x7f06009c" />
    <public type="dimen" name="material_emphasis_disabled" id="0x7f06009d" />
    <public type="dimen" name="material_emphasis_high_type" id="0x7f06009e" />
    <public type="dimen" name="material_emphasis_medium" id="0x7f06009f" />
    <public type="dimen" name="material_text_view_test_line_height" id="0x7f0600a0" />
    <public type="dimen" name="material_text_view_test_line_height_override" id="0x7f0600a1" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_bottom" id="0x7f0600a2" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_end" id="0x7f0600a3" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_start" id="0x7f0600a4" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_top" id="0x7f0600a5" />
    <public type="dimen" name="mtrl_alert_dialog_picker_background_inset" id="0x7f0600a6" />
    <public type="dimen" name="mtrl_badge_horizontal_edge_offset" id="0x7f0600a7" />
    <public type="dimen" name="mtrl_badge_long_text_horizontal_padding" id="0x7f0600a8" />
    <public type="dimen" name="mtrl_badge_radius" id="0x7f0600a9" />
    <public type="dimen" name="mtrl_badge_text_horizontal_edge_offset" id="0x7f0600aa" />
    <public type="dimen" name="mtrl_badge_text_size" id="0x7f0600ab" />
    <public type="dimen" name="mtrl_badge_with_text_radius" id="0x7f0600ac" />
    <public type="dimen" name="mtrl_bottomappbar_fabOffsetEndMode" id="0x7f0600ad" />
    <public type="dimen" name="mtrl_bottomappbar_fab_bottom_margin" id="0x7f0600ae" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_margin" id="0x7f0600af" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius" id="0x7f0600b0" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_vertical_offset" id="0x7f0600b1" />
    <public type="dimen" name="mtrl_bottomappbar_height" id="0x7f0600b2" />
    <public type="dimen" name="mtrl_btn_corner_radius" id="0x7f0600b3" />
    <public type="dimen" name="mtrl_btn_dialog_btn_min_width" id="0x7f0600b4" />
    <public type="dimen" name="mtrl_btn_disabled_elevation" id="0x7f0600b5" />
    <public type="dimen" name="mtrl_btn_disabled_z" id="0x7f0600b6" />
    <public type="dimen" name="mtrl_btn_elevation" id="0x7f0600b7" />
    <public type="dimen" name="mtrl_btn_focused_z" id="0x7f0600b8" />
    <public type="dimen" name="mtrl_btn_hovered_z" id="0x7f0600b9" />
    <public type="dimen" name="mtrl_btn_icon_btn_padding_left" id="0x7f0600ba" />
    <public type="dimen" name="mtrl_btn_icon_padding" id="0x7f0600bb" />
    <public type="dimen" name="mtrl_btn_inset" id="0x7f0600bc" />
    <public type="dimen" name="mtrl_btn_letter_spacing" id="0x7f0600bd" />
    <public type="dimen" name="mtrl_btn_padding_bottom" id="0x7f0600be" />
    <public type="dimen" name="mtrl_btn_padding_left" id="0x7f0600bf" />
    <public type="dimen" name="mtrl_btn_padding_right" id="0x7f0600c0" />
    <public type="dimen" name="mtrl_btn_padding_top" id="0x7f0600c1" />
    <public type="dimen" name="mtrl_btn_pressed_z" id="0x7f0600c2" />
    <public type="dimen" name="mtrl_btn_stroke_size" id="0x7f0600c3" />
    <public type="dimen" name="mtrl_btn_text_btn_icon_padding" id="0x7f0600c4" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_left" id="0x7f0600c5" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_right" id="0x7f0600c6" />
    <public type="dimen" name="mtrl_btn_text_size" id="0x7f0600c7" />
    <public type="dimen" name="mtrl_btn_z" id="0x7f0600c8" />
    <public type="dimen" name="mtrl_calendar_action_height" id="0x7f0600c9" />
    <public type="dimen" name="mtrl_calendar_action_padding" id="0x7f0600ca" />
    <public type="dimen" name="mtrl_calendar_bottom_padding" id="0x7f0600cb" />
    <public type="dimen" name="mtrl_calendar_content_padding" id="0x7f0600cc" />
    <public type="dimen" name="mtrl_calendar_day_corner" id="0x7f0600cd" />
    <public type="dimen" name="mtrl_calendar_day_height" id="0x7f0600ce" />
    <public type="dimen" name="mtrl_calendar_day_horizontal_padding" id="0x7f0600cf" />
    <public type="dimen" name="mtrl_calendar_day_today_stroke" id="0x7f0600d0" />
    <public type="dimen" name="mtrl_calendar_day_vertical_padding" id="0x7f0600d1" />
    <public type="dimen" name="mtrl_calendar_day_width" id="0x7f0600d2" />
    <public type="dimen" name="mtrl_calendar_days_of_week_height" id="0x7f0600d3" />
    <public type="dimen" name="mtrl_calendar_dialog_background_inset" id="0x7f0600d4" />
    <public type="dimen" name="mtrl_calendar_header_content_padding" id="0x7f0600d5" />
    <public type="dimen" name="mtrl_calendar_header_content_padding_fullscreen" id="0x7f0600d6" />
    <public type="dimen" name="mtrl_calendar_header_divider_thickness" id="0x7f0600d7" />
    <public type="dimen" name="mtrl_calendar_header_height" id="0x7f0600d8" />
    <public type="dimen" name="mtrl_calendar_header_height_fullscreen" id="0x7f0600d9" />
    <public type="dimen" name="mtrl_calendar_header_selection_line_height" id="0x7f0600da" />
    <public type="dimen" name="mtrl_calendar_header_text_padding" id="0x7f0600db" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_bottom" id="0x7f0600dc" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_top" id="0x7f0600dd" />
    <public type="dimen" name="mtrl_calendar_landscape_header_width" id="0x7f0600de" />
    <public type="dimen" name="mtrl_calendar_maximum_default_fullscreen_minor_axis" id="0x7f0600df" />
    <public type="dimen" name="mtrl_calendar_month_horizontal_padding" id="0x7f0600e0" />
    <public type="dimen" name="mtrl_calendar_month_vertical_padding" id="0x7f0600e1" />
    <public type="dimen" name="mtrl_calendar_navigation_bottom_padding" id="0x7f0600e2" />
    <public type="dimen" name="mtrl_calendar_navigation_height" id="0x7f0600e3" />
    <public type="dimen" name="mtrl_calendar_navigation_top_padding" id="0x7f0600e4" />
    <public type="dimen" name="mtrl_calendar_pre_l_text_clip_padding" id="0x7f0600e5" />
    <public type="dimen" name="mtrl_calendar_selection_baseline_to_top_fullscreen" id="0x7f0600e6" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_bottom" id="0x7f0600e7" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_bottom_fullscreen" id="0x7f0600e8" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_top" id="0x7f0600e9" />
    <public type="dimen" name="mtrl_calendar_text_input_padding_top" id="0x7f0600ea" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top" id="0x7f0600eb" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top_fullscreen" id="0x7f0600ec" />
    <public type="dimen" name="mtrl_calendar_year_corner" id="0x7f0600ed" />
    <public type="dimen" name="mtrl_calendar_year_height" id="0x7f0600ee" />
    <public type="dimen" name="mtrl_calendar_year_horizontal_padding" id="0x7f0600ef" />
    <public type="dimen" name="mtrl_calendar_year_vertical_padding" id="0x7f0600f0" />
    <public type="dimen" name="mtrl_calendar_year_width" id="0x7f0600f1" />
    <public type="dimen" name="mtrl_card_checked_icon_margin" id="0x7f0600f2" />
    <public type="dimen" name="mtrl_card_checked_icon_size" id="0x7f0600f3" />
    <public type="dimen" name="mtrl_card_corner_radius" id="0x7f0600f4" />
    <public type="dimen" name="mtrl_card_dragged_z" id="0x7f0600f5" />
    <public type="dimen" name="mtrl_card_elevation" id="0x7f0600f6" />
    <public type="dimen" name="mtrl_card_spacing" id="0x7f0600f7" />
    <public type="dimen" name="mtrl_chip_pressed_translation_z" id="0x7f0600f8" />
    <public type="dimen" name="mtrl_chip_text_size" id="0x7f0600f9" />
    <public type="dimen" name="mtrl_edittext_rectangle_top_offset" id="0x7f0600fa" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_elevation" id="0x7f0600fb" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_offset" id="0x7f0600fc" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_padding" id="0x7f0600fd" />
    <public type="dimen" name="mtrl_extended_fab_bottom_padding" id="0x7f0600fe" />
    <public type="dimen" name="mtrl_extended_fab_corner_radius" id="0x7f0600ff" />
    <public type="dimen" name="mtrl_extended_fab_disabled_elevation" id="0x7f060100" />
    <public type="dimen" name="mtrl_extended_fab_disabled_translation_z" id="0x7f060101" />
    <public type="dimen" name="mtrl_extended_fab_elevation" id="0x7f060102" />
    <public type="dimen" name="mtrl_extended_fab_end_padding" id="0x7f060103" />
    <public type="dimen" name="mtrl_extended_fab_end_padding_icon" id="0x7f060104" />
    <public type="dimen" name="mtrl_extended_fab_icon_size" id="0x7f060105" />
    <public type="dimen" name="mtrl_extended_fab_icon_text_spacing" id="0x7f060106" />
    <public type="dimen" name="mtrl_extended_fab_min_height" id="0x7f060107" />
    <public type="dimen" name="mtrl_extended_fab_min_width" id="0x7f060108" />
    <public type="dimen" name="mtrl_extended_fab_start_padding" id="0x7f060109" />
    <public type="dimen" name="mtrl_extended_fab_start_padding_icon" id="0x7f06010a" />
    <public type="dimen" name="mtrl_extended_fab_top_padding" id="0x7f06010b" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_base" id="0x7f06010c" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_hovered_focused" id="0x7f06010d" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_pressed" id="0x7f06010e" />
    <public type="dimen" name="mtrl_fab_elevation" id="0x7f06010f" />
    <public type="dimen" name="mtrl_fab_min_touch_target" id="0x7f060110" />
    <public type="dimen" name="mtrl_fab_translation_z_hovered_focused" id="0x7f060111" />
    <public type="dimen" name="mtrl_fab_translation_z_pressed" id="0x7f060112" />
    <public type="dimen" name="mtrl_high_ripple_default_alpha" id="0x7f060113" />
    <public type="dimen" name="mtrl_high_ripple_focused_alpha" id="0x7f060114" />
    <public type="dimen" name="mtrl_high_ripple_hovered_alpha" id="0x7f060115" />
    <public type="dimen" name="mtrl_high_ripple_pressed_alpha" id="0x7f060116" />
    <public type="dimen" name="mtrl_large_touch_target" id="0x7f060117" />
    <public type="dimen" name="mtrl_low_ripple_default_alpha" id="0x7f060118" />
    <public type="dimen" name="mtrl_low_ripple_focused_alpha" id="0x7f060119" />
    <public type="dimen" name="mtrl_low_ripple_hovered_alpha" id="0x7f06011a" />
    <public type="dimen" name="mtrl_low_ripple_pressed_alpha" id="0x7f06011b" />
    <public type="dimen" name="mtrl_min_touch_target_size" id="0x7f06011c" />
    <public type="dimen" name="mtrl_navigation_elevation" id="0x7f06011d" />
    <public type="dimen" name="mtrl_navigation_item_horizontal_padding" id="0x7f06011e" />
    <public type="dimen" name="mtrl_navigation_item_icon_padding" id="0x7f06011f" />
    <public type="dimen" name="mtrl_navigation_item_icon_size" id="0x7f060120" />
    <public type="dimen" name="mtrl_navigation_item_shape_horizontal_margin" id="0x7f060121" />
    <public type="dimen" name="mtrl_navigation_item_shape_vertical_margin" id="0x7f060122" />
    <public type="dimen" name="mtrl_shape_corner_size_large_component" id="0x7f060123" />
    <public type="dimen" name="mtrl_shape_corner_size_medium_component" id="0x7f060124" />
    <public type="dimen" name="mtrl_shape_corner_size_small_component" id="0x7f060125" />
    <public type="dimen" name="mtrl_slider_halo_radius" id="0x7f060126" />
    <public type="dimen" name="mtrl_slider_label_padding" id="0x7f060127" />
    <public type="dimen" name="mtrl_slider_label_radius" id="0x7f060128" />
    <public type="dimen" name="mtrl_slider_label_square_side" id="0x7f060129" />
    <public type="dimen" name="mtrl_slider_thumb_elevation" id="0x7f06012a" />
    <public type="dimen" name="mtrl_slider_thumb_radius" id="0x7f06012b" />
    <public type="dimen" name="mtrl_slider_track_height" id="0x7f06012c" />
    <public type="dimen" name="mtrl_slider_track_side_padding" id="0x7f06012d" />
    <public type="dimen" name="mtrl_slider_track_top" id="0x7f06012e" />
    <public type="dimen" name="mtrl_slider_widget_height" id="0x7f06012f" />
    <public type="dimen" name="mtrl_snackbar_action_text_color_alpha" id="0x7f060130" />
    <public type="dimen" name="mtrl_snackbar_background_corner_radius" id="0x7f060131" />
    <public type="dimen" name="mtrl_snackbar_background_overlay_color_alpha" id="0x7f060132" />
    <public type="dimen" name="mtrl_snackbar_margin" id="0x7f060133" />
    <public type="dimen" name="mtrl_switch_thumb_elevation" id="0x7f060134" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_medium" id="0x7f060135" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_small" id="0x7f060136" />
    <public type="dimen" name="mtrl_textinput_box_label_cutout_padding" id="0x7f060137" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_default" id="0x7f060138" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_focused" id="0x7f060139" />
    <public type="dimen" name="mtrl_textinput_counter_margin_start" id="0x7f06013a" />
    <public type="dimen" name="mtrl_textinput_end_icon_margin_start" id="0x7f06013b" />
    <public type="dimen" name="mtrl_textinput_outline_box_expanded_padding" id="0x7f06013c" />
    <public type="dimen" name="mtrl_textinput_start_icon_margin_end" id="0x7f06013d" />
    <public type="dimen" name="mtrl_toolbar_default_height" id="0x7f06013e" />
    <public type="dimen" name="mtrl_tooltip_arrowSize" id="0x7f06013f" />
    <public type="dimen" name="mtrl_tooltip_cornerSize" id="0x7f060140" />
    <public type="dimen" name="mtrl_tooltip_minHeight" id="0x7f060141" />
    <public type="dimen" name="mtrl_tooltip_minWidth" id="0x7f060142" />
    <public type="dimen" name="mtrl_tooltip_padding" id="0x7f060143" />
    <public type="dimen" name="mtrl_transition_shared_axis_slide_distance" id="0x7f060144" />
    <public type="dimen" name="notification_action_icon_size" id="0x7f060145" />
    <public type="dimen" name="notification_action_text_size" id="0x7f060146" />
    <public type="dimen" name="notification_big_circle_margin" id="0x7f060147" />
    <public type="dimen" name="notification_content_margin_start" id="0x7f060148" />
    <public type="dimen" name="notification_large_icon_height" id="0x7f060149" />
    <public type="dimen" name="notification_large_icon_width" id="0x7f06014a" />
    <public type="dimen" name="notification_main_column_padding_top" id="0x7f06014b" />
    <public type="dimen" name="notification_media_narrow_margin" id="0x7f06014c" />
    <public type="dimen" name="notification_right_icon_size" id="0x7f06014d" />
    <public type="dimen" name="notification_right_side_padding_top" id="0x7f06014e" />
    <public type="dimen" name="notification_small_icon_background_padding" id="0x7f06014f" />
    <public type="dimen" name="notification_small_icon_size_as_large" id="0x7f060150" />
    <public type="dimen" name="notification_subtext_size" id="0x7f060151" />
    <public type="dimen" name="notification_top_pad" id="0x7f060152" />
    <public type="dimen" name="notification_top_pad_large_text" id="0x7f060153" />
    <public type="dimen" name="subtitle_corner_radius" id="0x7f060154" />
    <public type="dimen" name="subtitle_outline_width" id="0x7f060155" />
    <public type="dimen" name="subtitle_shadow_offset" id="0x7f060156" />
    <public type="dimen" name="subtitle_shadow_radius" id="0x7f060157" />
    <public type="dimen" name="test_mtrl_calendar_day_cornerSize" id="0x7f060158" />
    <public type="dimen" name="tianmu_video_controller_height" id="0x7f060159" />
    <public type="dimen" name="tianmu_video_controller_icon_padding" id="0x7f06015a" />
    <public type="dimen" name="tianmu_video_controller_seekbar_max_size" id="0x7f06015b" />
    <public type="dimen" name="tianmu_video_controller_seekbar_size_n" id="0x7f06015c" />
    <public type="dimen" name="tianmu_video_controller_seekbar_size_s" id="0x7f06015d" />
    <public type="dimen" name="tianmu_video_controller_text_size" id="0x7f06015e" />
    <public type="dimen" name="tianmu_video_controller_time_text_size" id="0x7f06015f" />
    <public type="dimen" name="tianmu_video_default_spacing" id="0x7f060160" />
    <public type="dimen" name="tianmu_video_play_btn_size" id="0x7f060161" />
    <public type="dimen" name="tooltip_corner_radius" id="0x7f060162" />
    <public type="dimen" name="tooltip_horizontal_padding" id="0x7f060163" />
    <public type="dimen" name="tooltip_margin" id="0x7f060164" />
    <public type="dimen" name="tooltip_precise_anchor_extra_offset" id="0x7f060165" />
    <public type="dimen" name="tooltip_precise_anchor_threshold" id="0x7f060166" />
    <public type="dimen" name="tooltip_vertical_padding" id="0x7f060167" />
    <public type="dimen" name="tooltip_y_offset_non_touch" id="0x7f060168" />
    <public type="dimen" name="tooltip_y_offset_touch" id="0x7f060169" />
    <public type="drawable" name="res_0x7f070000_avd_hide_password__0" id="0x7f070000" />
    <public type="drawable" name="res_0x7f070001_avd_hide_password__1" id="0x7f070001" />
    <public type="drawable" name="res_0x7f070002_avd_hide_password__2" id="0x7f070002" />
    <public type="drawable" name="res_0x7f070003_avd_show_password__0" id="0x7f070003" />
    <public type="drawable" name="res_0x7f070004_avd_show_password__1" id="0x7f070004" />
    <public type="drawable" name="res_0x7f070005_avd_show_password__2" id="0x7f070005" />
    <public type="drawable" name="res_0x7f070006_ic_launcher_foreground__0" id="0x7f070006" />
    <public type="drawable" name="abc_ab_share_pack_mtrl_alpha" id="0x7f070007" />
    <public type="drawable" name="abc_action_bar_item_background_material" id="0x7f070008" />
    <public type="drawable" name="abc_btn_borderless_material" id="0x7f070009" />
    <public type="drawable" name="abc_btn_check_material" id="0x7f07000a" />
    <public type="drawable" name="abc_btn_check_material_anim" id="0x7f07000b" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_000" id="0x7f07000c" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_015" id="0x7f07000d" />
    <public type="drawable" name="abc_btn_colored_material" id="0x7f07000e" />
    <public type="drawable" name="abc_btn_default_mtrl_shape" id="0x7f07000f" />
    <public type="drawable" name="abc_btn_radio_material" id="0x7f070010" />
    <public type="drawable" name="abc_btn_radio_material_anim" id="0x7f070011" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_000" id="0x7f070012" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_015" id="0x7f070013" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00001" id="0x7f070014" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00012" id="0x7f070015" />
    <public type="drawable" name="abc_cab_background_internal_bg" id="0x7f070016" />
    <public type="drawable" name="abc_cab_background_top_material" id="0x7f070017" />
    <public type="drawable" name="abc_cab_background_top_mtrl_alpha" id="0x7f070018" />
    <public type="drawable" name="abc_control_background_material" id="0x7f070019" />
    <public type="drawable" name="abc_dialog_material_background" id="0x7f07001a" />
    <public type="drawable" name="abc_edit_text_material" id="0x7f07001b" />
    <public type="drawable" name="abc_ic_ab_back_material" id="0x7f07001c" />
    <public type="drawable" name="abc_ic_arrow_drop_right_black_24dp" id="0x7f07001d" />
    <public type="drawable" name="abc_ic_clear_material" id="0x7f07001e" />
    <public type="drawable" name="abc_ic_commit_search_api_mtrl_alpha" id="0x7f07001f" />
    <public type="drawable" name="abc_ic_go_search_api_material" id="0x7f070020" />
    <public type="drawable" name="abc_ic_menu_copy_mtrl_am_alpha" id="0x7f070021" />
    <public type="drawable" name="abc_ic_menu_cut_mtrl_alpha" id="0x7f070022" />
    <public type="drawable" name="abc_ic_menu_overflow_material" id="0x7f070023" />
    <public type="drawable" name="abc_ic_menu_paste_mtrl_am_alpha" id="0x7f070024" />
    <public type="drawable" name="abc_ic_menu_selectall_mtrl_alpha" id="0x7f070025" />
    <public type="drawable" name="abc_ic_menu_share_mtrl_alpha" id="0x7f070026" />
    <public type="drawable" name="abc_ic_search_api_material" id="0x7f070027" />
    <public type="drawable" name="abc_ic_voice_search_api_material" id="0x7f070028" />
    <public type="drawable" name="abc_item_background_holo_dark" id="0x7f070029" />
    <public type="drawable" name="abc_item_background_holo_light" id="0x7f07002a" />
    <public type="drawable" name="abc_list_divider_material" id="0x7f07002b" />
    <public type="drawable" name="abc_list_divider_mtrl_alpha" id="0x7f07002c" />
    <public type="drawable" name="abc_list_focused_holo" id="0x7f07002d" />
    <public type="drawable" name="abc_list_longpressed_holo" id="0x7f07002e" />
    <public type="drawable" name="abc_list_pressed_holo_dark" id="0x7f07002f" />
    <public type="drawable" name="abc_list_pressed_holo_light" id="0x7f070030" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_dark" id="0x7f070031" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_light" id="0x7f070032" />
    <public type="drawable" name="abc_list_selector_disabled_holo_dark" id="0x7f070033" />
    <public type="drawable" name="abc_list_selector_disabled_holo_light" id="0x7f070034" />
    <public type="drawable" name="abc_list_selector_holo_dark" id="0x7f070035" />
    <public type="drawable" name="abc_list_selector_holo_light" id="0x7f070036" />
    <public type="drawable" name="abc_menu_hardkey_panel_mtrl_mult" id="0x7f070037" />
    <public type="drawable" name="abc_popup_background_mtrl_mult" id="0x7f070038" />
    <public type="drawable" name="abc_ratingbar_indicator_material" id="0x7f070039" />
    <public type="drawable" name="abc_ratingbar_material" id="0x7f07003a" />
    <public type="drawable" name="abc_ratingbar_small_material" id="0x7f07003b" />
    <public type="drawable" name="abc_scrubber_control_off_mtrl_alpha" id="0x7f07003c" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000" id="0x7f07003d" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005" id="0x7f07003e" />
    <public type="drawable" name="abc_scrubber_primary_mtrl_alpha" id="0x7f07003f" />
    <public type="drawable" name="abc_scrubber_track_mtrl_alpha" id="0x7f070040" />
    <public type="drawable" name="abc_seekbar_thumb_material" id="0x7f070041" />
    <public type="drawable" name="abc_seekbar_tick_mark_material" id="0x7f070042" />
    <public type="drawable" name="abc_seekbar_track_material" id="0x7f070043" />
    <public type="drawable" name="abc_spinner_mtrl_am_alpha" id="0x7f070044" />
    <public type="drawable" name="abc_spinner_textfield_background_material" id="0x7f070045" />
    <public type="drawable" name="abc_star_black_48dp" id="0x7f070046" />
    <public type="drawable" name="abc_star_half_black_48dp" id="0x7f070047" />
    <public type="drawable" name="abc_switch_thumb_material" id="0x7f070048" />
    <public type="drawable" name="abc_switch_track_mtrl_alpha" id="0x7f070049" />
    <public type="drawable" name="abc_tab_indicator_material" id="0x7f07004a" />
    <public type="drawable" name="abc_tab_indicator_mtrl_alpha" id="0x7f07004b" />
    <public type="drawable" name="abc_text_cursor_material" id="0x7f07004c" />
    <public type="drawable" name="abc_text_select_handle_left_mtrl" id="0x7f07004d" />
    <public type="drawable" name="abc_text_select_handle_middle_mtrl" id="0x7f07004e" />
    <public type="drawable" name="abc_text_select_handle_right_mtrl" id="0x7f07004f" />
    <public type="drawable" name="abc_textfield_activated_mtrl_alpha" id="0x7f070050" />
    <public type="drawable" name="abc_textfield_default_mtrl_alpha" id="0x7f070051" />
    <public type="drawable" name="abc_textfield_search_activated_mtrl_alpha" id="0x7f070052" />
    <public type="drawable" name="abc_textfield_search_default_mtrl_alpha" id="0x7f070053" />
    <public type="drawable" name="abc_textfield_search_material" id="0x7f070054" />
    <public type="drawable" name="abc_vector_test" id="0x7f070055" />
    <public type="drawable" name="avd_hide_password" id="0x7f070056" />
    <public type="drawable" name="avd_show_password" id="0x7f070057" />
    <public type="drawable" name="bg_pay_tip" id="0x7f070058" />
    <public type="drawable" name="bg_red_num" id="0x7f070059" />
    <public type="drawable" name="bg_remain_reward" id="0x7f07005a" />
    <public type="drawable" name="bg_right_tip" id="0x7f07005b" />
    <public type="drawable" name="bg_tip_center" id="0x7f07005c" />
    <public type="drawable" name="bg_tip_status" id="0x7f07005d" />
    <public type="drawable" name="btn_back" id="0x7f07005e" />
    <public type="drawable" name="btn_checkbox_checked_mtrl" id="0x7f07005f" />
    <public type="drawable" name="btn_checkbox_checked_to_unchecked_mtrl_animation" id="0x7f070060" />
    <public type="drawable" name="btn_checkbox_unchecked_mtrl" id="0x7f070061" />
    <public type="drawable" name="btn_checkbox_unchecked_to_checked_mtrl_animation" id="0x7f070062" />
    <public type="drawable" name="btn_radio_off_mtrl" id="0x7f070063" />
    <public type="drawable" name="btn_radio_off_to_on_mtrl_animation" id="0x7f070064" />
    <public type="drawable" name="btn_radio_on_mtrl" id="0x7f070065" />
    <public type="drawable" name="btn_radio_on_to_off_mtrl_animation" id="0x7f070066" />
    <public type="drawable" name="design_bottom_navigation_item_background" id="0x7f070067" />
    <public type="drawable" name="design_fab_background" id="0x7f070068" />
    <public type="drawable" name="design_ic_visibility" id="0x7f070069" />
    <public type="drawable" name="design_ic_visibility_off" id="0x7f07006a" />
    <public type="drawable" name="design_password_eye" id="0x7f07006b" />
    <public type="drawable" name="design_snackbar_background" id="0x7f07006c" />
    <public type="drawable" name="ic_launcher_background" id="0x7f07006d" />
    <public type="drawable" name="ic_launcher_foreground" id="0x7f07006e" />
    <public type="drawable" name="ic_mtrl_checked_circle" id="0x7f07006f" />
    <public type="drawable" name="ic_mtrl_chip_checked_black" id="0x7f070070" />
    <public type="drawable" name="ic_mtrl_chip_checked_circle" id="0x7f070071" />
    <public type="drawable" name="ic_mtrl_chip_close_circle" id="0x7f070072" />
    <public type="drawable" name="material_ic_calendar_black_24dp" id="0x7f070073" />
    <public type="drawable" name="material_ic_clear_black_24dp" id="0x7f070074" />
    <public type="drawable" name="material_ic_edit_black_24dp" id="0x7f070075" />
    <public type="drawable" name="material_ic_keyboard_arrow_left_black_24dp" id="0x7f070076" />
    <public type="drawable" name="material_ic_keyboard_arrow_right_black_24dp" id="0x7f070077" />
    <public type="drawable" name="material_ic_menu_arrow_down_black_24dp" id="0x7f070078" />
    <public type="drawable" name="material_ic_menu_arrow_up_black_24dp" id="0x7f070079" />
    <public type="drawable" name="mtrl_dialog_background" id="0x7f07007a" />
    <public type="drawable" name="mtrl_dropdown_arrow" id="0x7f07007b" />
    <public type="drawable" name="mtrl_ic_arrow_drop_down" id="0x7f07007c" />
    <public type="drawable" name="mtrl_ic_arrow_drop_up" id="0x7f07007d" />
    <public type="drawable" name="mtrl_ic_cancel" id="0x7f07007e" />
    <public type="drawable" name="mtrl_ic_error" id="0x7f07007f" />
    <public type="drawable" name="mtrl_popupmenu_background" id="0x7f070080" />
    <public type="drawable" name="mtrl_popupmenu_background_dark" id="0x7f070081" />
    <public type="drawable" name="mtrl_tabs_default_indicator" id="0x7f070082" />
    <public type="drawable" name="navigation_empty_icon" id="0x7f070083" />
    <public type="drawable" name="notification_action_background" id="0x7f070084" />
    <public type="drawable" name="notification_bg" id="0x7f070085" />
    <public type="drawable" name="notification_bg_low" id="0x7f070086" />
    <public type="drawable" name="notification_bg_low_normal" id="0x7f070087" />
    <public type="drawable" name="notification_bg_low_pressed" id="0x7f070088" />
    <public type="drawable" name="notification_bg_normal" id="0x7f070089" />
    <public type="drawable" name="notification_bg_normal_pressed" id="0x7f07008a" />
    <public type="drawable" name="notification_icon_background" id="0x7f07008b" />
    <public type="drawable" name="notification_template_icon_bg" id="0x7f07008c" />
    <public type="drawable" name="notification_template_icon_low_bg" id="0x7f07008d" />
    <public type="drawable" name="notification_tile_bg" id="0x7f07008e" />
    <public type="drawable" name="notify_panel_notification_icon_bg" id="0x7f07008f" />
    <public type="drawable" name="permission_description_popup_bg" id="0x7f070090" />
    <public type="drawable" name="pro_wowan_webview_detail" id="0x7f070091" />
    <public type="drawable" name="pro_wowan_webview_index" id="0x7f070092" />
    <public type="drawable" name="progress_background" id="0x7f070093" />
    <public type="drawable" name="progress_bar" id="0x7f070094" />
    <public type="drawable" name="progressbar_horizontal" id="0x7f070095" />
    <public type="drawable" name="qiehuz_taojin_title_back" id="0x7f070096" />
    <public type="drawable" name="send_img" id="0x7f070097" />
    <public type="drawable" name="test_custom_background" id="0x7f070098" />
    <public type="drawable" name="test_level_drawable" id="0x7f070099" />
    <public type="drawable" name="test_toast_background" id="0x7f07009a" />
    <public type="drawable" name="tianmu_activity_transparent_bg" id="0x7f07009b" />
    <public type="drawable" name="tianmu_bg_background_1b72e6_radius4" id="0x7f07009c" />
    <public type="drawable" name="tianmu_bg_banner_action_button" id="0x7f07009d" />
    <public type="drawable" name="tianmu_bg_interstitial_gradient" id="0x7f07009e" />
    <public type="drawable" name="tianmu_bg_native_adapter_splash" id="0x7f07009f" />
    <public type="drawable" name="tianmu_bg_reward_action_bar_gradient_end" id="0x7f0700a0" />
    <public type="drawable" name="tianmu_bg_reward_action_bar_gradient_start" id="0x7f0700a1" />
    <public type="drawable" name="tianmu_bg_splash_action_button" id="0x7f0700a2" />
    <public type="drawable" name="tianmu_bg_splash_action_button_blue" id="0x7f0700a3" />
    <public type="drawable" name="tianmu_bg_splash_gradient" id="0x7f0700a4" />
    <public type="drawable" name="tianmu_bg_splash_gradient1" id="0x7f0700a5" />
    <public type="drawable" name="tianmu_bg_splash_gradient2" id="0x7f0700a6" />
    <public type="drawable" name="tianmu_bg_splash_gradient3" id="0x7f0700a7" />
    <public type="drawable" name="tianmu_download_chevron_right_black_24dp" id="0x7f0700a8" />
    <public type="drawable" name="tianmu_download_confirm_background_confirm" id="0x7f0700a9" />
    <public type="drawable" name="tianmu_download_confirm_background_landscape" id="0x7f0700aa" />
    <public type="drawable" name="tianmu_download_confirm_background_portrait" id="0x7f0700ab" />
    <public type="drawable" name="tianmu_download_ic_pause_24dp" id="0x7f0700ac" />
    <public type="drawable" name="tianmu_download_ic_play_24dp" id="0x7f0700ad" />
    <public type="drawable" name="tianmu_erase_finger" id="0x7f0700ae" />
    <public type="drawable" name="tianmu_erase_path" id="0x7f0700af" />
    <public type="drawable" name="tianmu_gradient_45000000_radius20" id="0x7f0700b0" />
    <public type="drawable" name="tianmu_icon_ad_install" id="0x7f0700b1" />
    <public type="drawable" name="tianmu_icon_back" id="0x7f0700b2" />
    <public type="drawable" name="tianmu_icon_close" id="0x7f0700b3" />
    <public type="drawable" name="tianmu_icon_direction_bg" id="0x7f0700b4" />
    <public type="drawable" name="tianmu_icon_notice_pause" id="0x7f0700b5" />
    <public type="drawable" name="tianmu_icon_notice_pause_transparent" id="0x7f0700b6" />
    <public type="drawable" name="tianmu_icon_notice_start" id="0x7f0700b7" />
    <public type="drawable" name="tianmu_icon_notice_start_transparent" id="0x7f0700b8" />
    <public type="drawable" name="tianmu_icon_platform_icon" id="0x7f0700b9" />
    <public type="drawable" name="tianmu_icon_play" id="0x7f0700ba" />
    <public type="drawable" name="tianmu_icon_rec_round_close" id="0x7f0700bb" />
    <public type="drawable" name="tianmu_icon_right_arrow" id="0x7f0700bc" />
    <public type="drawable" name="tianmu_icon_round_close" id="0x7f0700bd" />
    <public type="drawable" name="tianmu_progress_bar" id="0x7f0700be" />
    <public type="drawable" name="tianmu_progress_bar_bg_border1_radius4" id="0x7f0700bf" />
    <public type="drawable" name="tianmu_reward_close" id="0x7f0700c0" />
    <public type="drawable" name="tianmu_reward_mute" id="0x7f0700c1" />
    <public type="drawable" name="tianmu_reward_voice" id="0x7f0700c2" />
    <public type="drawable" name="tianmu_selector_blue_pressed" id="0x7f0700c3" />
    <public type="drawable" name="tianmu_selector_red_pressed" id="0x7f0700c4" />
    <public type="drawable" name="tianmu_shake_phone" id="0x7f0700c5" />
    <public type="drawable" name="tianmu_shake_phone_tip" id="0x7f0700c6" />
    <public type="drawable" name="tianmu_shape_000000_radius20" id="0x7f0700c7" />
    <public type="drawable" name="tianmu_shape_1b72e6_radius4" id="0x7f0700c8" />
    <public type="drawable" name="tianmu_shape_33000000_radius8" id="0x7f0700c9" />
    <public type="drawable" name="tianmu_shape_45000000_radius20" id="0x7f0700ca" />
    <public type="drawable" name="tianmu_shape_555454_radius7" id="0x7f0700cb" />
    <public type="drawable" name="tianmu_shape_75000000_radius2" id="0x7f0700cc" />
    <public type="drawable" name="tianmu_shape_75000000_radius20" id="0x7f0700cd" />
    <public type="drawable" name="tianmu_shape_75cccccc_circle" id="0x7f0700ce" />
    <public type="drawable" name="tianmu_shape_96000000_radius7" id="0x7f0700cf" />
    <public type="drawable" name="tianmu_shape_a1ffffff_radius16" id="0x7f0700d0" />
    <public type="drawable" name="tianmu_shape_cbffffff_radius16" id="0x7f0700d1" />
    <public type="drawable" name="tianmu_shape_download_dialog_bgd_bar" id="0x7f0700d2" />
    <public type="drawable" name="tianmu_shape_download_dialog_progressbar" id="0x7f0700d3" />
    <public type="drawable" name="tianmu_shape_download_pause_radius8" id="0x7f0700d4" />
    <public type="drawable" name="tianmu_shape_downloading_back_radius8" id="0x7f0700d5" />
    <public type="drawable" name="tianmu_shape_downloading_radius8" id="0x7f0700d6" />
    <public type="drawable" name="tianmu_shape_ed3646_radius4" id="0x7f0700d7" />
    <public type="drawable" name="tianmu_shape_fa6400_radius4" id="0x7f0700d8" />
    <public type="drawable" name="tianmu_shape_ff0091ff_radius4" id="0x7f0700d9" />
    <public type="drawable" name="tianmu_shape_ff3790ef_radius36" id="0x7f0700da" />
    <public type="drawable" name="tianmu_shape_ff6f553d_radius20" id="0x7f0700db" />
    <public type="drawable" name="tianmu_shape_ff7c9eb9_radius36" id="0x7f0700dc" />
    <public type="drawable" name="tianmu_shape_ffda50_radius4" id="0x7f0700dd" />
    <public type="drawable" name="tianmu_shape_ffed3646_radius10" id="0x7f0700de" />
    <public type="drawable" name="tianmu_shape_fff9f9f9_radius4" id="0x7f0700df" />
    <public type="drawable" name="tianmu_shape_ffffff_radius20" id="0x7f0700e0" />
    <public type="drawable" name="tianmu_shape_ffffffff_radius12" id="0x7f0700e1" />
    <public type="drawable" name="tianmu_shape_ffffffff_radius4" id="0x7f0700e2" />
    <public type="drawable" name="tianmu_shape_sway_perfect_circle_bg" id="0x7f0700e3" />
    <public type="drawable" name="tianmu_shape_teetertotter_bg" id="0x7f0700e4" />
    <public type="drawable" name="tianmu_sliding_animal_figer" id="0x7f0700e5" />
    <public type="drawable" name="tianmu_sliding_screen_figer" id="0x7f0700e6" />
    <public type="drawable" name="tianmu_splash_bottom_arc" id="0x7f0700e7" />
    <public type="drawable" name="tianmu_splash_bottom_arc_slide_icon" id="0x7f0700e8" />
    <public type="drawable" name="tianmu_sway_icon_phone" id="0x7f0700e9" />
    <public type="drawable" name="tianmu_sway_left_arrow_bg" id="0x7f0700ea" />
    <public type="drawable" name="tianmu_sway_left_arrow_fill" id="0x7f0700eb" />
    <public type="drawable" name="tianmu_sway_right_arrow_bg" id="0x7f0700ec" />
    <public type="drawable" name="tianmu_sway_right_arrow_fill" id="0x7f0700ed" />
    <public type="drawable" name="tianmu_teetertotter_def_circle" id="0x7f0700ee" />
    <public type="drawable" name="tianmu_text_cbffffff_underline" id="0x7f0700ef" />
    <public type="drawable" name="tianmu_video_ic_action_close" id="0x7f0700f0" />
    <public type="drawable" name="tianmu_video_ic_action_pause" id="0x7f0700f1" />
    <public type="drawable" name="tianmu_video_ic_action_play_arrow" id="0x7f0700f2" />
    <public type="drawable" name="tianmu_video_ic_action_replay" id="0x7f0700f3" />
    <public type="drawable" name="tianmu_video_progress_loading" id="0x7f0700f4" />
    <public type="drawable" name="tianmu_video_seekbar_thumb" id="0x7f0700f5" />
    <public type="drawable" name="tianmu_video_seekbar_thumb_normal" id="0x7f0700f6" />
    <public type="drawable" name="tianmu_video_seekbar_thumb_pressed" id="0x7f0700f7" />
    <public type="drawable" name="tianmu_video_selector_play_button" id="0x7f0700f8" />
    <public type="drawable" name="tianmu_video_shape_back_bg" id="0x7f0700f9" />
    <public type="drawable" name="tianmu_video_shape_play_bg" id="0x7f0700fa" />
    <public type="drawable" name="tianmu_video_shape_standard_controller_top_bg" id="0x7f0700fb" />
    <public type="drawable" name="tianmu_video_shape_stardard_controller_bottom_bg" id="0x7f0700fc" />
    <public type="drawable" name="tianmu_video_shape_status_view_btn" id="0x7f0700fd" />
    <public type="drawable" name="tooltip_frame_dark" id="0x7f0700fe" />
    <public type="drawable" name="tooltip_frame_light" id="0x7f0700ff" />
    <public type="drawable" name="utils_toast_bg" id="0x7f070100" />
    <public type="drawable" name="v1" id="0x7f070101" />
    <public type="drawable" name="v2" id="0x7f070102" />
    <public type="drawable" name="v3" id="0x7f070103" />
    <public type="drawable" name="v4" id="0x7f070104" />
    <public type="drawable" name="v5" id="0x7f070105" />
    <public type="drawable" name="vector_video_play" id="0x7f070106" />
    <public type="id" name="ALT" id="0x7f080000" />
    <public type="id" name="BOTTOM_END" id="0x7f080001" />
    <public type="id" name="BOTTOM_START" id="0x7f080002" />
    <public type="id" name="CTRL" id="0x7f080003" />
    <public type="id" name="FUNCTION" id="0x7f080004" />
    <public type="id" name="META" id="0x7f080005" />
    <public type="id" name="SHIFT" id="0x7f080006" />
    <public type="id" name="SYM" id="0x7f080007" />
    <public type="id" name="TOP_END" id="0x7f080008" />
    <public type="id" name="TOP_START" id="0x7f080009" />
    <public type="id" name="accessibility_action_clickable_span" id="0x7f08000a" />
    <public type="id" name="accessibility_custom_action_0" id="0x7f08000b" />
    <public type="id" name="accessibility_custom_action_1" id="0x7f08000c" />
    <public type="id" name="accessibility_custom_action_10" id="0x7f08000d" />
    <public type="id" name="accessibility_custom_action_11" id="0x7f08000e" />
    <public type="id" name="accessibility_custom_action_12" id="0x7f08000f" />
    <public type="id" name="accessibility_custom_action_13" id="0x7f080010" />
    <public type="id" name="accessibility_custom_action_14" id="0x7f080011" />
    <public type="id" name="accessibility_custom_action_15" id="0x7f080012" />
    <public type="id" name="accessibility_custom_action_16" id="0x7f080013" />
    <public type="id" name="accessibility_custom_action_17" id="0x7f080014" />
    <public type="id" name="accessibility_custom_action_18" id="0x7f080015" />
    <public type="id" name="accessibility_custom_action_19" id="0x7f080016" />
    <public type="id" name="accessibility_custom_action_2" id="0x7f080017" />
    <public type="id" name="accessibility_custom_action_20" id="0x7f080018" />
    <public type="id" name="accessibility_custom_action_21" id="0x7f080019" />
    <public type="id" name="accessibility_custom_action_22" id="0x7f08001a" />
    <public type="id" name="accessibility_custom_action_23" id="0x7f08001b" />
    <public type="id" name="accessibility_custom_action_24" id="0x7f08001c" />
    <public type="id" name="accessibility_custom_action_25" id="0x7f08001d" />
    <public type="id" name="accessibility_custom_action_26" id="0x7f08001e" />
    <public type="id" name="accessibility_custom_action_27" id="0x7f08001f" />
    <public type="id" name="accessibility_custom_action_28" id="0x7f080020" />
    <public type="id" name="accessibility_custom_action_29" id="0x7f080021" />
    <public type="id" name="accessibility_custom_action_3" id="0x7f080022" />
    <public type="id" name="accessibility_custom_action_30" id="0x7f080023" />
    <public type="id" name="accessibility_custom_action_31" id="0x7f080024" />
    <public type="id" name="accessibility_custom_action_4" id="0x7f080025" />
    <public type="id" name="accessibility_custom_action_5" id="0x7f080026" />
    <public type="id" name="accessibility_custom_action_6" id="0x7f080027" />
    <public type="id" name="accessibility_custom_action_7" id="0x7f080028" />
    <public type="id" name="accessibility_custom_action_8" id="0x7f080029" />
    <public type="id" name="accessibility_custom_action_9" id="0x7f08002a" />
    <public type="id" name="action0" id="0x7f08002b" />
    <public type="id" name="action_bar" id="0x7f08002c" />
    <public type="id" name="action_bar_activity_content" id="0x7f08002d" />
    <public type="id" name="action_bar_container" id="0x7f08002e" />
    <public type="id" name="action_bar_root" id="0x7f08002f" />
    <public type="id" name="action_bar_spinner" id="0x7f080030" />
    <public type="id" name="action_bar_subtitle" id="0x7f080031" />
    <public type="id" name="action_bar_title" id="0x7f080032" />
    <public type="id" name="action_container" id="0x7f080033" />
    <public type="id" name="action_context_bar" id="0x7f080034" />
    <public type="id" name="action_divider" id="0x7f080035" />
    <public type="id" name="action_image" id="0x7f080036" />
    <public type="id" name="action_menu_divider" id="0x7f080037" />
    <public type="id" name="action_menu_presenter" id="0x7f080038" />
    <public type="id" name="action_mode_bar" id="0x7f080039" />
    <public type="id" name="action_mode_bar_stub" id="0x7f08003a" />
    <public type="id" name="action_mode_close_button" id="0x7f08003b" />
    <public type="id" name="action_text" id="0x7f08003c" />
    <public type="id" name="actions" id="0x7f08003d" />
    <public type="id" name="activity_chooser_view_content" id="0x7f08003e" />
    <public type="id" name="adContainer" id="0x7f08003f" />
    <public type="id" name="add" id="0x7f080040" />
    <public type="id" name="alertTitle" id="0x7f080041" />
    <public type="id" name="all" id="0x7f080042" />
    <public type="id" name="alreadyGetMoney" id="0x7f080043" />
    <public type="id" name="alreadyGetMoneyBox" id="0x7f080044" />
    <public type="id" name="always" id="0x7f080045" />
    <public type="id" name="async" id="0x7f080046" />
    <public type="id" name="auto" id="0x7f080047" />
    <public type="id" name="barrier" id="0x7f080048" />
    <public type="id" name="base_popup_content_root" id="0x7f080049" />
    <public type="id" name="beginning" id="0x7f08004a" />
    <public type="id" name="blocking" id="0x7f08004b" />
    <public type="id" name="bottom" id="0x7f08004c" />
    <public type="id" name="bottom_to_top" id="0x7f08004d" />
    <public type="id" name="btn_back" id="0x7f08004e" />
    <public type="id" name="buttonPanel" id="0x7f08004f" />
    <public type="id" name="cancel_action" id="0x7f080050" />
    <public type="id" name="cancel_button" id="0x7f080051" />
    <public type="id" name="center" id="0x7f080052" />
    <public type="id" name="center_horizontal" id="0x7f080053" />
    <public type="id" name="center_vertical" id="0x7f080054" />
    <public type="id" name="chains" id="0x7f080055" />
    <public type="id" name="checkbox" id="0x7f080056" />
    <public type="id" name="checked" id="0x7f080057" />
    <public type="id" name="chip" id="0x7f080058" />
    <public type="id" name="chip1" id="0x7f080059" />
    <public type="id" name="chip2" id="0x7f08005a" />
    <public type="id" name="chip3" id="0x7f08005b" />
    <public type="id" name="chip_group" id="0x7f08005c" />
    <public type="id" name="chronometer" id="0x7f08005d" />
    <public type="id" name="clear_text" id="0x7f08005e" />
    <public type="id" name="clip_horizontal" id="0x7f08005f" />
    <public type="id" name="clip_vertical" id="0x7f080060" />
    <public type="id" name="collapseActionView" id="0x7f080061" />
    <public type="id" name="confirm_button" id="0x7f080062" />
    <public type="id" name="container" id="0x7f080063" />
    <public type="id" name="content" id="0x7f080064" />
    <public type="id" name="contentPanel" id="0x7f080065" />
    <public type="id" name="content_alliance_ad_container" id="0x7f080066" />
    <public type="id" name="coordinator" id="0x7f080067" />
    <public type="id" name="custom" id="0x7f080068" />
    <public type="id" name="customPanel" id="0x7f080069" />
    <public type="id" name="cut" id="0x7f08006a" />
    <public type="id" name="date_picker_actions" id="0x7f08006b" />
    <public type="id" name="decor_content_parent" id="0x7f08006c" />
    <public type="id" name="default_activity_button" id="0x7f08006d" />
    <public type="id" name="design_bottom_sheet" id="0x7f08006e" />
    <public type="id" name="design_menu_item_action_area" id="0x7f08006f" />
    <public type="id" name="design_menu_item_action_area_stub" id="0x7f080070" />
    <public type="id" name="design_menu_item_text" id="0x7f080071" />
    <public type="id" name="design_navigation_view" id="0x7f080072" />
    <public type="id" name="dialog_button" id="0x7f080073" />
    <public type="id" name="dimensions" id="0x7f080074" />
    <public type="id" name="direct" id="0x7f080075" />
    <public type="id" name="disableHome" id="0x7f080076" />
    <public type="id" name="dropdown_menu" id="0x7f080077" />
    <public type="id" name="edit_query" id="0x7f080078" />
    <public type="id" name="end" id="0x7f080079" />
    <public type="id" name="end_padder" id="0x7f08007a" />
    <public type="id" name="enterAlways" id="0x7f08007b" />
    <public type="id" name="enterAlwaysCollapsed" id="0x7f08007c" />
    <public type="id" name="exitUntilCollapsed" id="0x7f08007d" />
    <public type="id" name="expand_activities_button" id="0x7f08007e" />
    <public type="id" name="expanded_menu" id="0x7f08007f" />
    <public type="id" name="fade" id="0x7f080080" />
    <public type="id" name="fill" id="0x7f080081" />
    <public type="id" name="fill_horizontal" id="0x7f080082" />
    <public type="id" name="fill_vertical" id="0x7f080083" />
    <public type="id" name="filled" id="0x7f080084" />
    <public type="id" name="fitToContents" id="0x7f080085" />
    <public type="id" name="fixed" id="0x7f080086" />
    <public type="id" name="fl_container" id="0x7f080087" />
    <public type="id" name="fl_load_mask" id="0x7f080088" />
    <public type="id" name="fl_update_warn" id="0x7f080089" />
    <public type="id" name="floating" id="0x7f08008a" />
    <public type="id" name="forever" id="0x7f08008b" />
    <public type="id" name="fragment_container_view_tag" id="0x7f08008c" />
    <public type="id" name="frameLayoutId" id="0x7f08008d" />
    <public type="id" name="frameLayoutId_detail" id="0x7f08008e" />
    <public type="id" name="ghost_view" id="0x7f08008f" />
    <public type="id" name="ghost_view_holder" id="0x7f080090" />
    <public type="id" name="glide_custom_view_target_tag" id="0x7f080091" />
    <public type="id" name="gone" id="0x7f080092" />
    <public type="id" name="group_divider" id="0x7f080093" />
    <public type="id" name="groups" id="0x7f080094" />
    <public type="id" name="hideable" id="0x7f080095" />
    <public type="id" name="home" id="0x7f080096" />
    <public type="id" name="homeAsUp" id="0x7f080097" />
    <public type="id" name="icon" id="0x7f080098" />
    <public type="id" name="icon_group" id="0x7f080099" />
    <public type="id" name="ifRoom" id="0x7f08009a" />
    <public type="id" name="image" id="0x7f08009b" />
    <public type="id" name="info" id="0x7f08009c" />
    <public type="id" name="invisible" id="0x7f08009d" />
    <public type="id" name="italic" id="0x7f08009e" />
    <public type="id" name="item_touch_helper_previous_elevation" id="0x7f08009f" />
    <public type="id" name="iv_close" id="0x7f0800a0" />
    <public type="id" name="kbl_close_keyboard" id="0x7f0800a1" />
    <public type="id" name="kbl_keyboard_listener" id="0x7f0800a2" />
    <public type="id" name="kbl_keyboard_opened" id="0x7f0800a3" />
    <public type="id" name="kbl_open_keyboard" id="0x7f0800a4" />
    <public type="id" name="kbl_origin_height" id="0x7f0800a5" />
    <public type="id" name="kbl_origin_visible_height" id="0x7f0800a6" />
    <public type="id" name="kbl_visible_height" id="0x7f0800a7" />
    <public type="id" name="labeled" id="0x7f0800a8" />
    <public type="id" name="largeLabel" id="0x7f0800a9" />
    <public type="id" name="left" id="0x7f0800aa" />
    <public type="id" name="left_to_right" id="0x7f0800ab" />
    <public type="id" name="line1" id="0x7f0800ac" />
    <public type="id" name="line3" id="0x7f0800ad" />
    <public type="id" name="linear" id="0x7f0800ae" />
    <public type="id" name="listMode" id="0x7f0800af" />
    <public type="id" name="list_item" id="0x7f0800b0" />
    <public type="id" name="ll_remain_num" id="0x7f0800b1" />
    <public type="id" name="ll_reward2" id="0x7f0800b2" />
    <public type="id" name="ll_tip" id="0x7f0800b3" />
    <public type="id" name="load_box" id="0x7f0800b4" />
    <public type="id" name="main_srl" id="0x7f0800b5" />
    <public type="id" name="main_srl_detail" id="0x7f0800b6" />
    <public type="id" name="masked" id="0x7f0800b7" />
    <public type="id" name="media_actions" id="0x7f0800b8" />
    <public type="id" name="message" id="0x7f0800b9" />
    <public type="id" name="middle" id="0x7f0800ba" />
    <public type="id" name="mini" id="0x7f0800bb" />
    <public type="id" name="month_grid" id="0x7f0800bc" />
    <public type="id" name="month_navigation_bar" id="0x7f0800bd" />
    <public type="id" name="month_navigation_fragment_toggle" id="0x7f0800be" />
    <public type="id" name="month_navigation_next" id="0x7f0800bf" />
    <public type="id" name="month_navigation_previous" id="0x7f0800c0" />
    <public type="id" name="month_title" id="0x7f0800c1" />
    <public type="id" name="mtrl_calendar_day_selector_frame" id="0x7f0800c2" />
    <public type="id" name="mtrl_calendar_days_of_week" id="0x7f0800c3" />
    <public type="id" name="mtrl_calendar_frame" id="0x7f0800c4" />
    <public type="id" name="mtrl_calendar_main_pane" id="0x7f0800c5" />
    <public type="id" name="mtrl_calendar_months" id="0x7f0800c6" />
    <public type="id" name="mtrl_calendar_selection_frame" id="0x7f0800c7" />
    <public type="id" name="mtrl_calendar_text_input_frame" id="0x7f0800c8" />
    <public type="id" name="mtrl_calendar_year_selector_frame" id="0x7f0800c9" />
    <public type="id" name="mtrl_card_checked_layer_id" id="0x7f0800ca" />
    <public type="id" name="mtrl_child_content_container" id="0x7f0800cb" />
    <public type="id" name="mtrl_internal_children_alpha_tag" id="0x7f0800cc" />
    <public type="id" name="mtrl_motion_snapshot_view" id="0x7f0800cd" />
    <public type="id" name="mtrl_picker_fullscreen" id="0x7f0800ce" />
    <public type="id" name="mtrl_picker_header" id="0x7f0800cf" />
    <public type="id" name="mtrl_picker_header_selection_text" id="0x7f0800d0" />
    <public type="id" name="mtrl_picker_header_title_and_selection" id="0x7f0800d1" />
    <public type="id" name="mtrl_picker_header_toggle" id="0x7f0800d2" />
    <public type="id" name="mtrl_picker_text_input_date" id="0x7f0800d3" />
    <public type="id" name="mtrl_picker_text_input_range_end" id="0x7f0800d4" />
    <public type="id" name="mtrl_picker_text_input_range_start" id="0x7f0800d5" />
    <public type="id" name="mtrl_picker_title_text" id="0x7f0800d6" />
    <public type="id" name="multiply" id="0x7f0800d7" />
    <public type="id" name="navigation_header_container" id="0x7f0800d8" />
    <public type="id" name="never" id="0x7f0800d9" />
    <public type="id" name="noScroll" id="0x7f0800da" />
    <public type="id" name="none" id="0x7f0800db" />
    <public type="id" name="normal" id="0x7f0800dc" />
    <public type="id" name="notification_background" id="0x7f0800dd" />
    <public type="id" name="notification_main_column" id="0x7f0800de" />
    <public type="id" name="notification_main_column_container" id="0x7f0800df" />
    <public type="id" name="off" id="0x7f0800e0" />
    <public type="id" name="on" id="0x7f0800e1" />
    <public type="id" name="outline" id="0x7f0800e2" />
    <public type="id" name="packed" id="0x7f0800e3" />
    <public type="id" name="parallax" id="0x7f0800e4" />
    <public type="id" name="parent" id="0x7f0800e5" />
    <public type="id" name="parentPanel" id="0x7f0800e6" />
    <public type="id" name="parent_matrix" id="0x7f0800e7" />
    <public type="id" name="password_toggle" id="0x7f0800e8" />
    <public type="id" name="pb" id="0x7f0800e9" />
    <public type="id" name="peekHeight" id="0x7f0800ea" />
    <public type="id" name="percent" id="0x7f0800eb" />
    <public type="id" name="pin" id="0x7f0800ec" />
    <public type="id" name="pro_webview" id="0x7f0800ed" />
    <public type="id" name="progressBar" id="0x7f0800ee" />
    <public type="id" name="progress_bar" id="0x7f0800ef" />
    <public type="id" name="progress_circular" id="0x7f0800f0" />
    <public type="id" name="progress_horizontal" id="0x7f0800f1" />
    <public type="id" name="radial" id="0x7f0800f2" />
    <public type="id" name="radio" id="0x7f0800f3" />
    <public type="id" name="restart" id="0x7f0800f4" />
    <public type="id" name="reverse" id="0x7f0800f5" />
    <public type="id" name="right" id="0x7f0800f6" />
    <public type="id" name="right_icon" id="0x7f0800f7" />
    <public type="id" name="right_side" id="0x7f0800f8" />
    <public type="id" name="right_to_left" id="0x7f0800f9" />
    <public type="id" name="rl_loading" id="0x7f0800fa" />
    <public type="id" name="rl_main" id="0x7f0800fb" />
    <public type="id" name="rl_pay" id="0x7f0800fc" />
    <public type="id" name="rl_top" id="0x7f0800fd" />
    <public type="id" name="rounded" id="0x7f0800fe" />
    <public type="id" name="row_index_key" id="0x7f0800ff" />
    <public type="id" name="save_non_transition_alpha" id="0x7f080100" />
    <public type="id" name="save_overlay_view" id="0x7f080101" />
    <public type="id" name="scale" id="0x7f080102" />
    <public type="id" name="screen" id="0x7f080103" />
    <public type="id" name="scroll" id="0x7f080104" />
    <public type="id" name="scrollIndicatorDown" id="0x7f080105" />
    <public type="id" name="scrollIndicatorUp" id="0x7f080106" />
    <public type="id" name="scrollView" id="0x7f080107" />
    <public type="id" name="scrollable" id="0x7f080108" />
    <public type="id" name="search_badge" id="0x7f080109" />
    <public type="id" name="search_bar" id="0x7f08010a" />
    <public type="id" name="search_button" id="0x7f08010b" />
    <public type="id" name="search_close_btn" id="0x7f08010c" />
    <public type="id" name="search_edit_frame" id="0x7f08010d" />
    <public type="id" name="search_go_btn" id="0x7f08010e" />
    <public type="id" name="search_mag_icon" id="0x7f08010f" />
    <public type="id" name="search_plate" id="0x7f080110" />
    <public type="id" name="search_src_text" id="0x7f080111" />
    <public type="id" name="search_voice_btn" id="0x7f080112" />
    <public type="id" name="select_dialog_listview" id="0x7f080113" />
    <public type="id" name="selected" id="0x7f080114" />
    <public type="id" name="shortcut" id="0x7f080115" />
    <public type="id" name="showCustom" id="0x7f080116" />
    <public type="id" name="showHome" id="0x7f080117" />
    <public type="id" name="showTitle" id="0x7f080118" />
    <public type="id" name="skipCollapsed" id="0x7f080119" />
    <public type="id" name="slide" id="0x7f08011a" />
    <public type="id" name="smallLabel" id="0x7f08011b" />
    <public type="id" name="snackbar_action" id="0x7f08011c" />
    <public type="id" name="snackbar_text" id="0x7f08011d" />
    <public type="id" name="snap" id="0x7f08011e" />
    <public type="id" name="snapMargins" id="0x7f08011f" />
    <public type="id" name="spacer" id="0x7f080120" />
    <public type="id" name="special_effects_controller_view_tag" id="0x7f080121" />
    <public type="id" name="split_action_bar" id="0x7f080122" />
    <public type="id" name="spread" id="0x7f080123" />
    <public type="id" name="spread_inside" id="0x7f080124" />
    <public type="id" name="src_atop" id="0x7f080125" />
    <public type="id" name="src_in" id="0x7f080126" />
    <public type="id" name="src_over" id="0x7f080127" />
    <public type="id" name="standard" id="0x7f080128" />
    <public type="id" name="start" id="0x7f080129" />
    <public type="id" name="status_bar_latest_event_content" id="0x7f08012a" />
    <public type="id" name="stretch" id="0x7f08012b" />
    <public type="id" name="submenuarrow" id="0x7f08012c" />
    <public type="id" name="submit_area" id="0x7f08012d" />
    <public type="id" name="tabMode" id="0x7f08012e" />
    <public type="id" name="tag_accessibility_actions" id="0x7f08012f" />
    <public type="id" name="tag_accessibility_clickable_spans" id="0x7f080130" />
    <public type="id" name="tag_accessibility_heading" id="0x7f080131" />
    <public type="id" name="tag_accessibility_pane_title" id="0x7f080132" />
    <public type="id" name="tag_on_apply_window_listener" id="0x7f080133" />
    <public type="id" name="tag_on_receive_content_listener" id="0x7f080134" />
    <public type="id" name="tag_on_receive_content_mime_types" id="0x7f080135" />
    <public type="id" name="tag_screen_reader_focusable" id="0x7f080136" />
    <public type="id" name="tag_state_description" id="0x7f080137" />
    <public type="id" name="tag_transition_group" id="0x7f080138" />
    <public type="id" name="tag_unhandled_key_event_manager" id="0x7f080139" />
    <public type="id" name="tag_unhandled_key_listeners" id="0x7f08013a" />
    <public type="id" name="tag_window_insets_animation_callback" id="0x7f08013b" />
    <public type="id" name="tbLayout" id="0x7f08013c" />
    <public type="id" name="test_checkbox_android_button_tint" id="0x7f08013d" />
    <public type="id" name="test_checkbox_app_button_tint" id="0x7f08013e" />
    <public type="id" name="test_radiobutton_android_button_tint" id="0x7f08013f" />
    <public type="id" name="test_radiobutton_app_button_tint" id="0x7f080140" />
    <public type="id" name="text" id="0x7f080141" />
    <public type="id" name="text2" id="0x7f080142" />
    <public type="id" name="textEnd" id="0x7f080143" />
    <public type="id" name="textSpacerNoButtons" id="0x7f080144" />
    <public type="id" name="textSpacerNoTitle" id="0x7f080145" />
    <public type="id" name="textStart" id="0x7f080146" />
    <public type="id" name="text_input_end_icon" id="0x7f080147" />
    <public type="id" name="text_input_start_icon" id="0x7f080148" />
    <public type="id" name="textinput_counter" id="0x7f080149" />
    <public type="id" name="textinput_error" id="0x7f08014a" />
    <public type="id" name="textinput_helper_text" id="0x7f08014b" />
    <public type="id" name="textinput_placeholder" id="0x7f08014c" />
    <public type="id" name="textinput_prefix_text" id="0x7f08014d" />
    <public type="id" name="textinput_suffix_text" id="0x7f08014e" />
    <public type="id" name="tianmu_app_permissions" id="0x7f08014f" />
    <public type="id" name="tianmu_banner_content_container" id="0x7f080150" />
    <public type="id" name="tianmu_banner_iv_close" id="0x7f080151" />
    <public type="id" name="tianmu_banner_iv_pic" id="0x7f080152" />
    <public type="id" name="tianmu_banner_tv_action_button" id="0x7f080153" />
    <public type="id" name="tianmu_banner_tv_ad_source" id="0x7f080154" />
    <public type="id" name="tianmu_banner_tv_ad_target" id="0x7f080155" />
    <public type="id" name="tianmu_banner_tv_desc" id="0x7f080156" />
    <public type="id" name="tianmu_banner_tv_title" id="0x7f080157" />
    <public type="id" name="tianmu_fl_canvas" id="0x7f080158" />
    <public type="id" name="tianmu_fl_container" id="0x7f080159" />
    <public type="id" name="tianmu_fl_interaction" id="0x7f08015a" />
    <public type="id" name="tianmu_fl_slide_container" id="0x7f08015b" />
    <public type="id" name="tianmu_fl_slide_view_mask" id="0x7f08015c" />
    <public type="id" name="tianmu_gravity_front" id="0x7f08015d" />
    <public type="id" name="tianmu_id_view_expose_tag" id="0x7f08015e" />
    <public type="id" name="tianmu_id_view_response" id="0x7f08015f" />
    <public type="id" name="tianmu_interstitial_container" id="0x7f080160" />
    <public type="id" name="tianmu_interstitial_envelope_paper_fl_container" id="0x7f080161" />
    <public type="id" name="tianmu_interstitial_envelope_paper_iv_cover" id="0x7f080162" />
    <public type="id" name="tianmu_interstitial_envelope_paper_iv_mini_image" id="0x7f080163" />
    <public type="id" name="tianmu_interstitial_envelope_paper_tv_desc" id="0x7f080164" />
    <public type="id" name="tianmu_interstitial_envelope_paper_tv_title" id="0x7f080165" />
    <public type="id" name="tianmu_interstitial_fl_click" id="0x7f080166" />
    <public type="id" name="tianmu_interstitial_fl_container" id="0x7f080167" />
    <public type="id" name="tianmu_interstitial_fl_envelope_paper_jump" id="0x7f080168" />
    <public type="id" name="tianmu_interstitial_full_screen_container" id="0x7f080169" />
    <public type="id" name="tianmu_interstitial_iv_envelope_cover" id="0x7f08016a" />
    <public type="id" name="tianmu_interstitial_iv_image" id="0x7f08016b" />
    <public type="id" name="tianmu_interstitial_iv_pic" id="0x7f08016c" />
    <public type="id" name="tianmu_interstitial_iv_skip" id="0x7f08016d" />
    <public type="id" name="tianmu_interstitial_ll_app_info_container" id="0x7f08016e" />
    <public type="id" name="tianmu_interstitial_ll_container" id="0x7f08016f" />
    <public type="id" name="tianmu_interstitial_rl_envelope_paper" id="0x7f080170" />
    <public type="id" name="tianmu_interstitial_tv_action" id="0x7f080171" />
    <public type="id" name="tianmu_interstitial_tv_app_info" id="0x7f080172" />
    <public type="id" name="tianmu_interstitial_tv_desc" id="0x7f080173" />
    <public type="id" name="tianmu_interstitial_tv_title" id="0x7f080174" />
    <public type="id" name="tianmu_interstitial_video_container" id="0x7f080175" />
    <public type="id" name="tianmu_interstitial_view_envelope_back" id="0x7f080176" />
    <public type="id" name="tianmu_interstitial_view_envelope_mask" id="0x7f080177" />
    <public type="id" name="tianmu_iv_close" id="0x7f080178" />
    <public type="id" name="tianmu_iv_curve_view" id="0x7f080179" />
    <public type="id" name="tianmu_iv_def_circle" id="0x7f08017a" />
    <public type="id" name="tianmu_iv_finger" id="0x7f08017b" />
    <public type="id" name="tianmu_iv_image" id="0x7f08017c" />
    <public type="id" name="tianmu_iv_splash_image" id="0x7f08017d" />
    <public type="id" name="tianmu_iv_video_mute" id="0x7f08017e" />
    <public type="id" name="tianmu_library_back_icon" id="0x7f08017f" />
    <public type="id" name="tianmu_library_backlayout" id="0x7f080180" />
    <public type="id" name="tianmu_library_close_tv" id="0x7f080181" />
    <public type="id" name="tianmu_library_content" id="0x7f080182" />
    <public type="id" name="tianmu_library_fl_click" id="0x7f080183" />
    <public type="id" name="tianmu_library_fl_reward_detention_dialog_container" id="0x7f080184" />
    <public type="id" name="tianmu_library_full_screen_container" id="0x7f080185" />
    <public type="id" name="tianmu_library_gradient_end" id="0x7f080186" />
    <public type="id" name="tianmu_library_gradient_start" id="0x7f080187" />
    <public type="id" name="tianmu_library_iv_ad_icon" id="0x7f080188" />
    <public type="id" name="tianmu_library_iv_app_icon" id="0x7f080189" />
    <public type="id" name="tianmu_library_iv_close" id="0x7f08018a" />
    <public type="id" name="tianmu_library_iv_image" id="0x7f08018b" />
    <public type="id" name="tianmu_library_iv_mute" id="0x7f08018c" />
    <public type="id" name="tianmu_library_iv_pause" id="0x7f08018d" />
    <public type="id" name="tianmu_library_iv_play" id="0x7f08018e" />
    <public type="id" name="tianmu_library_iv_skip" id="0x7f08018f" />
    <public type="id" name="tianmu_library_iv_small_interaction" id="0x7f080190" />
    <public type="id" name="tianmu_library_json_loading" id="0x7f080191" />
    <public type="id" name="tianmu_library_layout_webView" id="0x7f080192" />
    <public type="id" name="tianmu_library_ll_ad_content" id="0x7f080193" />
    <public type="id" name="tianmu_library_ll_ad_webview_container" id="0x7f080194" />
    <public type="id" name="tianmu_library_ll_parent_container" id="0x7f080195" />
    <public type="id" name="tianmu_library_ll_target" id="0x7f080196" />
    <public type="id" name="tianmu_library_pb_progress" id="0x7f080197" />
    <public type="id" name="tianmu_library_progress_bar" id="0x7f080198" />
    <public type="id" name="tianmu_library_rl_ad_content" id="0x7f080199" />
    <public type="id" name="tianmu_library_rl_cover" id="0x7f08019a" />
    <public type="id" name="tianmu_library_rl_download_pause_layout" id="0x7f08019b" />
    <public type="id" name="tianmu_library_rl_downloading_layout" id="0x7f08019c" />
    <public type="id" name="tianmu_library_rl_parent" id="0x7f08019d" />
    <public type="id" name="tianmu_library_rl_title" id="0x7f08019e" />
    <public type="id" name="tianmu_library_rl_video_container" id="0x7f08019f" />
    <public type="id" name="tianmu_library_title" id="0x7f0801a0" />
    <public type="id" name="tianmu_library_tv_action" id="0x7f0801a1" />
    <public type="id" name="tianmu_library_tv_action_bg" id="0x7f0801a2" />
    <public type="id" name="tianmu_library_tv_action_text" id="0x7f0801a3" />
    <public type="id" name="tianmu_library_tv_ad_app_developer" id="0x7f0801a4" />
    <public type="id" name="tianmu_library_tv_ad_app_permissions" id="0x7f0801a5" />
    <public type="id" name="tianmu_library_tv_ad_btn_container" id="0x7f0801a6" />
    <public type="id" name="tianmu_library_tv_ad_desc" id="0x7f0801a7" />
    <public type="id" name="tianmu_library_tv_ad_downloading_desc" id="0x7f0801a8" />
    <public type="id" name="tianmu_library_tv_ad_pause_desc" id="0x7f0801a9" />
    <public type="id" name="tianmu_library_tv_ad_source" id="0x7f0801aa" />
    <public type="id" name="tianmu_library_tv_ad_status" id="0x7f0801ab" />
    <public type="id" name="tianmu_library_tv_ad_target" id="0x7f0801ac" />
    <public type="id" name="tianmu_library_tv_ad_title" id="0x7f0801ad" />
    <public type="id" name="tianmu_library_tv_ad_version" id="0x7f0801ae" />
    <public type="id" name="tianmu_library_tv_ad_webview_back" id="0x7f0801af" />
    <public type="id" name="tianmu_library_tv_app_info" id="0x7f0801b0" />
    <public type="id" name="tianmu_library_tv_btn_function" id="0x7f0801b1" />
    <public type="id" name="tianmu_library_tv_btn_function_bg" id="0x7f0801b2" />
    <public type="id" name="tianmu_library_tv_continue_watch" id="0x7f0801b3" />
    <public type="id" name="tianmu_library_tv_count_down" id="0x7f0801b4" />
    <public type="id" name="tianmu_library_tv_desc" id="0x7f0801b5" />
    <public type="id" name="tianmu_library_tv_exit" id="0x7f0801b6" />
    <public type="id" name="tianmu_library_tv_function" id="0x7f0801b7" />
    <public type="id" name="tianmu_library_tv_tips" id="0x7f0801b8" />
    <public type="id" name="tianmu_library_tv_title" id="0x7f0801b9" />
    <public type="id" name="tianmu_library_video_fullView" id="0x7f0801ba" />
    <public type="id" name="tianmu_library_view_gradient_end" id="0x7f0801bb" />
    <public type="id" name="tianmu_library_view_gradient_start" id="0x7f0801bc" />
    <public type="id" name="tianmu_library_webview_info" id="0x7f0801bd" />
    <public type="id" name="tianmu_library_webview_progress" id="0x7f0801be" />
    <public type="id" name="tianmu_ll_download_list_empty" id="0x7f0801bf" />
    <public type="id" name="tianmu_ll_splash_ad_content" id="0x7f0801c0" />
    <public type="id" name="tianmu_nir_iv_cover" id="0x7f0801c1" />
    <public type="id" name="tianmu_nir_notice_ad_container" id="0x7f0801c2" />
    <public type="id" name="tianmu_nir_tv_do_not_remind" id="0x7f0801c3" />
    <public type="id" name="tianmu_nir_tv_install_now" id="0x7f0801c4" />
    <public type="id" name="tianmu_nir_tv_tips" id="0x7f0801c5" />
    <public type="id" name="tianmu_progressbar_container" id="0x7f0801c6" />
    <public type="id" name="tianmu_riv_logo" id="0x7f0801c7" />
    <public type="id" name="tianmu_rl_ad_container" id="0x7f0801c8" />
    <public type="id" name="tianmu_rl_ad_interact" id="0x7f0801c9" />
    <public type="id" name="tianmu_rl_animal" id="0x7f0801ca" />
    <public type="id" name="tianmu_rl_inner_ad_container" id="0x7f0801cb" />
    <public type="id" name="tianmu_rl_splash_container" id="0x7f0801cc" />
    <public type="id" name="tianmu_rv_download_list" id="0x7f0801cd" />
    <public type="id" name="tianmu_sfd_iv_bg" id="0x7f0801ce" />
    <public type="id" name="tianmu_sfd_iv_finger" id="0x7f0801cf" />
    <public type="id" name="tianmu_splash_action_button_container" id="0x7f0801d0" />
    <public type="id" name="tianmu_splash_arc_slide_image" id="0x7f0801d1" />
    <public type="id" name="tianmu_splash_iv_image" id="0x7f0801d2" />
    <public type="id" name="tianmu_splash_ll_container" id="0x7f0801d3" />
    <public type="id" name="tianmu_splash_tv_action_button" id="0x7f0801d4" />
    <public type="id" name="tianmu_splash_video_view" id="0x7f0801d5" />
    <public type="id" name="tianmu_status_download_app_image" id="0x7f0801d6" />
    <public type="id" name="tianmu_status_download_app_image_mask" id="0x7f0801d7" />
    <public type="id" name="tianmu_status_download_app_name" id="0x7f0801d8" />
    <public type="id" name="tianmu_status_download_container" id="0x7f0801d9" />
    <public type="id" name="tianmu_status_download_ll_btns" id="0x7f0801da" />
    <public type="id" name="tianmu_status_download_pause" id="0x7f0801db" />
    <public type="id" name="tianmu_status_download_progress_bar" id="0x7f0801dc" />
    <public type="id" name="tianmu_status_download_progress_tv" id="0x7f0801dd" />
    <public type="id" name="tianmu_status_download_start" id="0x7f0801de" />
    <public type="id" name="tianmu_status_download_status" id="0x7f0801df" />
    <public type="id" name="tianmu_status_download_stop" id="0x7f0801e0" />
    <public type="id" name="tianmu_sway_progressbar" id="0x7f0801e1" />
    <public type="id" name="tianmu_sway_progressbar_root" id="0x7f0801e2" />
    <public type="id" name="tianmu_teetertotter_ll_container" id="0x7f0801e3" />
    <public type="id" name="tianmu_teetertotter_progressbar" id="0x7f0801e4" />
    <public type="id" name="tianmu_tsfl_slide" id="0x7f0801e5" />
    <public type="id" name="tianmu_tsfl_transparency_view" id="0x7f0801e6" />
    <public type="id" name="tianmu_tv_action" id="0x7f0801e7" />
    <public type="id" name="tianmu_tv_ad_source" id="0x7f0801e8" />
    <public type="id" name="tianmu_tv_ad_target" id="0x7f0801e9" />
    <public type="id" name="tianmu_tv_close" id="0x7f0801ea" />
    <public type="id" name="tianmu_tv_desc" id="0x7f0801eb" />
    <public type="id" name="tianmu_tv_splash_desc" id="0x7f0801ec" />
    <public type="id" name="tianmu_tv_tip" id="0x7f0801ed" />
    <public type="id" name="tianmu_tv_title" id="0x7f0801ee" />
    <public type="id" name="tianmu_type_16_9" id="0x7f0801ef" />
    <public type="id" name="tianmu_type_4_3" id="0x7f0801f0" />
    <public type="id" name="tianmu_type_center_crop" id="0x7f0801f1" />
    <public type="id" name="tianmu_type_default" id="0x7f0801f2" />
    <public type="id" name="tianmu_type_match_parent" id="0x7f0801f3" />
    <public type="id" name="tianmu_type_original" id="0x7f0801f4" />
    <public type="id" name="tianmu_video_complete_container" id="0x7f0801f5" />
    <public type="id" name="tianmu_video_iv_replay" id="0x7f0801f6" />
    <public type="id" name="tianmu_video_loading" id="0x7f0801f7" />
    <public type="id" name="tianmu_video_message" id="0x7f0801f8" />
    <public type="id" name="tianmu_video_net_warning_layout" id="0x7f0801f9" />
    <public type="id" name="tianmu_video_sound" id="0x7f0801fa" />
    <public type="id" name="tianmu_video_start_play" id="0x7f0801fb" />
    <public type="id" name="tianmu_video_status_btn" id="0x7f0801fc" />
    <public type="id" name="tianmu_video_thumb" id="0x7f0801fd" />
    <public type="id" name="tianmu_view_mask_shade" id="0x7f0801fe" />
    <public type="id" name="tianmu_webView" id="0x7f0801ff" />
    <public type="id" name="tianmu_widget_iv_close" id="0x7f080200" />
    <public type="id" name="tianmu_widget_iv_phone" id="0x7f080201" />
    <public type="id" name="tianmu_widget_iv_shake" id="0x7f080202" />
    <public type="id" name="tianmu_widget_iv_slide" id="0x7f080203" />
    <public type="id" name="tianmu_widget_ll_container" id="0x7f080204" />
    <public type="id" name="tianmu_widget_tv_interaction_tips" id="0x7f080205" />
    <public type="id" name="tianmu_widget_tv_jump_tips" id="0x7f080206" />
    <public type="id" name="tianmu_widget_tv_skip" id="0x7f080207" />
    <public type="id" name="time" id="0x7f080208" />
    <public type="id" name="title" id="0x7f080209" />
    <public type="id" name="titleDividerNoCustom" id="0x7f08020a" />
    <public type="id" name="title_back_btn" id="0x7f08020b" />
    <public type="id" name="title_bar" id="0x7f08020c" />
    <public type="id" name="title_template" id="0x7f08020d" />
    <public type="id" name="toolbar" id="0x7f08020e" />
    <public type="id" name="top" id="0x7f08020f" />
    <public type="id" name="topPanel" id="0x7f080210" />
    <public type="id" name="top_back" id="0x7f080211" />
    <public type="id" name="top_back_detail" id="0x7f080212" />
    <public type="id" name="top_to_bottom" id="0x7f080213" />
    <public type="id" name="touch_outside" id="0x7f080214" />
    <public type="id" name="transition_current_scene" id="0x7f080215" />
    <public type="id" name="transition_layout_save" id="0x7f080216" />
    <public type="id" name="transition_position" id="0x7f080217" />
    <public type="id" name="transition_scene_layoutid_cache" id="0x7f080218" />
    <public type="id" name="transition_transform" id="0x7f080219" />
    <public type="id" name="tv_permission_description_message" id="0x7f08021a" />
    <public type="id" name="tv_progress" id="0x7f08021b" />
    <public type="id" name="tv_red_num" id="0x7f08021c" />
    <public type="id" name="tv_tip" id="0x7f08021d" />
    <public type="id" name="tv_title" id="0x7f08021e" />
    <public type="id" name="tv_wowan_title" id="0x7f08021f" />
    <public type="id" name="tv_wowan_title_detail" id="0x7f080220" />
    <public type="id" name="unchecked" id="0x7f080221" />
    <public type="id" name="uniform" id="0x7f080222" />
    <public type="id" name="unlabeled" id="0x7f080223" />
    <public type="id" name="up" id="0x7f080224" />
    <public type="id" name="useLogo" id="0x7f080225" />
    <public type="id" name="utvBottomIconView" id="0x7f080226" />
    <public type="id" name="utvLeftIconView" id="0x7f080227" />
    <public type="id" name="utvRightIconView" id="0x7f080228" />
    <public type="id" name="utvTopIconView" id="0x7f080229" />
    <public type="id" name="versionchecklib_failed_dialog_cancel" id="0x7f08022a" />
    <public type="id" name="versionchecklib_failed_dialog_retry" id="0x7f08022b" />
    <public type="id" name="versionchecklib_loading_dialog_cancel" id="0x7f08022c" />
    <public type="id" name="versionchecklib_version_dialog_cancel" id="0x7f08022d" />
    <public type="id" name="versionchecklib_version_dialog_commit" id="0x7f08022e" />
    <public type="id" name="viewId" id="0x7f08022f" />
    <public type="id" name="viewId_detail" id="0x7f080230" />
    <public type="id" name="view_offset_helper" id="0x7f080231" />
    <public type="id" name="view_tree_lifecycle_owner" id="0x7f080232" />
    <public type="id" name="view_tree_on_back_pressed_dispatcher_owner" id="0x7f080233" />
    <public type="id" name="view_tree_saved_state_registry_owner" id="0x7f080234" />
    <public type="id" name="view_tree_view_model_store_owner" id="0x7f080235" />
    <public type="id" name="vipBgBox" id="0x7f080236" />
    <public type="id" name="visible" id="0x7f080237" />
    <public type="id" name="visible_removing_fragment_view_tag" id="0x7f080238" />
    <public type="id" name="wall_progress_bar" id="0x7f080239" />
    <public type="id" name="web_view" id="0x7f08023a" />
    <public type="id" name="webview" id="0x7f08023b" />
    <public type="id" name="webview_detail" id="0x7f08023c" />
    <public type="id" name="withText" id="0x7f08023d" />
    <public type="id" name="withinBounds" id="0x7f08023e" />
    <public type="id" name="wrap" id="0x7f08023f" />
    <public type="id" name="wrap_content" id="0x7f080240" />
    <public type="id" name="ww_statusbarutil_fake_status_bar_view" id="0x7f080241" />
    <public type="id" name="zero_corner_chip" id="0x7f080242" />
    <public type="integer" name="abc_config_activityDefaultDur" id="0x7f090000" />
    <public type="integer" name="abc_config_activityShortDur" id="0x7f090001" />
    <public type="integer" name="app_bar_elevation_anim_duration" id="0x7f090002" />
    <public type="integer" name="bottom_sheet_slide_duration" id="0x7f090003" />
    <public type="integer" name="cancel_button_image_alpha" id="0x7f090004" />
    <public type="integer" name="config_tooltipAnimTime" id="0x7f090005" />
    <public type="integer" name="design_snackbar_text_max_lines" id="0x7f090006" />
    <public type="integer" name="design_tab_indicator_anim_duration_ms" id="0x7f090007" />
    <public type="integer" name="hide_password_duration" id="0x7f090008" />
    <public type="integer" name="mtrl_badge_max_character_count" id="0x7f090009" />
    <public type="integer" name="mtrl_btn_anim_delay_ms" id="0x7f09000a" />
    <public type="integer" name="mtrl_btn_anim_duration_ms" id="0x7f09000b" />
    <public type="integer" name="mtrl_calendar_header_orientation" id="0x7f09000c" />
    <public type="integer" name="mtrl_calendar_selection_text_lines" id="0x7f09000d" />
    <public type="integer" name="mtrl_calendar_year_selector_span" id="0x7f09000e" />
    <public type="integer" name="mtrl_card_anim_delay_ms" id="0x7f09000f" />
    <public type="integer" name="mtrl_card_anim_duration_ms" id="0x7f090010" />
    <public type="integer" name="mtrl_chip_anim_duration" id="0x7f090011" />
    <public type="integer" name="mtrl_tab_indicator_anim_duration_ms" id="0x7f090012" />
    <public type="integer" name="show_password_duration" id="0x7f090013" />
    <public type="integer" name="status_bar_notification_info_maxnum" id="0x7f090014" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_0" id="0x7f0a0000" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_1" id="0x7f0a0001" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_0" id="0x7f0a0002" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_1" id="0x7f0a0003" />
    <public type="interpolator" name="btn_radio_to_off_mtrl_animation_interpolator_0" id="0x7f0a0004" />
    <public type="interpolator" name="btn_radio_to_on_mtrl_animation_interpolator_0" id="0x7f0a0005" />
    <public type="interpolator" name="fast_out_slow_in" id="0x7f0a0006" />
    <public type="interpolator" name="mtrl_fast_out_linear_in" id="0x7f0a0007" />
    <public type="interpolator" name="mtrl_fast_out_slow_in" id="0x7f0a0008" />
    <public type="interpolator" name="mtrl_linear" id="0x7f0a0009" />
    <public type="interpolator" name="mtrl_linear_out_slow_in" id="0x7f0a000a" />
    <public type="layout" name="abc_action_bar_title_item" id="0x7f0b0000" />
    <public type="layout" name="abc_action_bar_up_container" id="0x7f0b0001" />
    <public type="layout" name="abc_action_menu_item_layout" id="0x7f0b0002" />
    <public type="layout" name="abc_action_menu_layout" id="0x7f0b0003" />
    <public type="layout" name="abc_action_mode_bar" id="0x7f0b0004" />
    <public type="layout" name="abc_action_mode_close_item_material" id="0x7f0b0005" />
    <public type="layout" name="abc_activity_chooser_view" id="0x7f0b0006" />
    <public type="layout" name="abc_activity_chooser_view_list_item" id="0x7f0b0007" />
    <public type="layout" name="abc_alert_dialog_button_bar_material" id="0x7f0b0008" />
    <public type="layout" name="abc_alert_dialog_material" id="0x7f0b0009" />
    <public type="layout" name="abc_alert_dialog_title_material" id="0x7f0b000a" />
    <public type="layout" name="abc_cascading_menu_item_layout" id="0x7f0b000b" />
    <public type="layout" name="abc_dialog_title_material" id="0x7f0b000c" />
    <public type="layout" name="abc_expanded_menu_layout" id="0x7f0b000d" />
    <public type="layout" name="abc_list_menu_item_checkbox" id="0x7f0b000e" />
    <public type="layout" name="abc_list_menu_item_icon" id="0x7f0b000f" />
    <public type="layout" name="abc_list_menu_item_layout" id="0x7f0b0010" />
    <public type="layout" name="abc_list_menu_item_radio" id="0x7f0b0011" />
    <public type="layout" name="abc_popup_menu_header_item_layout" id="0x7f0b0012" />
    <public type="layout" name="abc_popup_menu_item_layout" id="0x7f0b0013" />
    <public type="layout" name="abc_screen_content_include" id="0x7f0b0014" />
    <public type="layout" name="abc_screen_simple" id="0x7f0b0015" />
    <public type="layout" name="abc_screen_simple_overlay_action_mode" id="0x7f0b0016" />
    <public type="layout" name="abc_screen_toolbar" id="0x7f0b0017" />
    <public type="layout" name="abc_search_dropdown_item_icons_2line" id="0x7f0b0018" />
    <public type="layout" name="abc_search_view" id="0x7f0b0019" />
    <public type="layout" name="abc_select_dialog_material" id="0x7f0b001a" />
    <public type="layout" name="abc_tooltip" id="0x7f0b001b" />
    <public type="layout" name="activity_contents" id="0x7f0b001c" />
    <public type="layout" name="activity_main" id="0x7f0b001d" />
    <public type="layout" name="activity_splash" id="0x7f0b001e" />
    <public type="layout" name="activity_tube" id="0x7f0b001f" />
    <public type="layout" name="activity_video" id="0x7f0b0020" />
    <public type="layout" name="activity_wall" id="0x7f0b0021" />
    <public type="layout" name="activity_wowan" id="0x7f0b0022" />
    <public type="layout" name="activity_wowan_detail" id="0x7f0b0023" />
    <public type="layout" name="custom_dialog" id="0x7f0b0024" />
    <public type="layout" name="design_bottom_navigation_item" id="0x7f0b0025" />
    <public type="layout" name="design_bottom_sheet_dialog" id="0x7f0b0026" />
    <public type="layout" name="design_layout_snackbar" id="0x7f0b0027" />
    <public type="layout" name="design_layout_snackbar_include" id="0x7f0b0028" />
    <public type="layout" name="design_layout_tab_icon" id="0x7f0b0029" />
    <public type="layout" name="design_layout_tab_text" id="0x7f0b002a" />
    <public type="layout" name="design_menu_item_action_area" id="0x7f0b002b" />
    <public type="layout" name="design_navigation_item" id="0x7f0b002c" />
    <public type="layout" name="design_navigation_item_header" id="0x7f0b002d" />
    <public type="layout" name="design_navigation_item_separator" id="0x7f0b002e" />
    <public type="layout" name="design_navigation_item_subheader" id="0x7f0b002f" />
    <public type="layout" name="design_navigation_menu" id="0x7f0b0030" />
    <public type="layout" name="design_navigation_menu_item" id="0x7f0b0031" />
    <public type="layout" name="design_text_input_end_icon" id="0x7f0b0032" />
    <public type="layout" name="design_text_input_start_icon" id="0x7f0b0033" />
    <public type="layout" name="downloading_layout" id="0x7f0b0034" />
    <public type="layout" name="mtrl_alert_dialog" id="0x7f0b0035" />
    <public type="layout" name="mtrl_alert_dialog_actions" id="0x7f0b0036" />
    <public type="layout" name="mtrl_alert_dialog_title" id="0x7f0b0037" />
    <public type="layout" name="mtrl_alert_select_dialog_item" id="0x7f0b0038" />
    <public type="layout" name="mtrl_alert_select_dialog_multichoice" id="0x7f0b0039" />
    <public type="layout" name="mtrl_alert_select_dialog_singlechoice" id="0x7f0b003a" />
    <public type="layout" name="mtrl_calendar_day" id="0x7f0b003b" />
    <public type="layout" name="mtrl_calendar_day_of_week" id="0x7f0b003c" />
    <public type="layout" name="mtrl_calendar_days_of_week" id="0x7f0b003d" />
    <public type="layout" name="mtrl_calendar_horizontal" id="0x7f0b003e" />
    <public type="layout" name="mtrl_calendar_month" id="0x7f0b003f" />
    <public type="layout" name="mtrl_calendar_month_labeled" id="0x7f0b0040" />
    <public type="layout" name="mtrl_calendar_month_navigation" id="0x7f0b0041" />
    <public type="layout" name="mtrl_calendar_months" id="0x7f0b0042" />
    <public type="layout" name="mtrl_calendar_vertical" id="0x7f0b0043" />
    <public type="layout" name="mtrl_calendar_year" id="0x7f0b0044" />
    <public type="layout" name="mtrl_layout_snackbar" id="0x7f0b0045" />
    <public type="layout" name="mtrl_layout_snackbar_include" id="0x7f0b0046" />
    <public type="layout" name="mtrl_picker_actions" id="0x7f0b0047" />
    <public type="layout" name="mtrl_picker_dialog" id="0x7f0b0048" />
    <public type="layout" name="mtrl_picker_fullscreen" id="0x7f0b0049" />
    <public type="layout" name="mtrl_picker_header_dialog" id="0x7f0b004a" />
    <public type="layout" name="mtrl_picker_header_fullscreen" id="0x7f0b004b" />
    <public type="layout" name="mtrl_picker_header_selection_text" id="0x7f0b004c" />
    <public type="layout" name="mtrl_picker_header_title_text" id="0x7f0b004d" />
    <public type="layout" name="mtrl_picker_header_toggle" id="0x7f0b004e" />
    <public type="layout" name="mtrl_picker_text_input_date" id="0x7f0b004f" />
    <public type="layout" name="mtrl_picker_text_input_date_range" id="0x7f0b0050" />
    <public type="layout" name="notification_action" id="0x7f0b0051" />
    <public type="layout" name="notification_action_tombstone" id="0x7f0b0052" />
    <public type="layout" name="notification_media_action" id="0x7f0b0053" />
    <public type="layout" name="notification_media_cancel_action" id="0x7f0b0054" />
    <public type="layout" name="notification_template_big_media" id="0x7f0b0055" />
    <public type="layout" name="notification_template_big_media_custom" id="0x7f0b0056" />
    <public type="layout" name="notification_template_big_media_narrow" id="0x7f0b0057" />
    <public type="layout" name="notification_template_big_media_narrow_custom" id="0x7f0b0058" />
    <public type="layout" name="notification_template_custom_big" id="0x7f0b0059" />
    <public type="layout" name="notification_template_icon_group" id="0x7f0b005a" />
    <public type="layout" name="notification_template_lines_media" id="0x7f0b005b" />
    <public type="layout" name="notification_template_media" id="0x7f0b005c" />
    <public type="layout" name="notification_template_media_custom" id="0x7f0b005d" />
    <public type="layout" name="notification_template_part_chronometer" id="0x7f0b005e" />
    <public type="layout" name="notification_template_part_time" id="0x7f0b005f" />
    <public type="layout" name="permission_description_popup" id="0x7f0b0060" />
    <public type="layout" name="popup_pay" id="0x7f0b0061" />
    <public type="layout" name="popup_tip" id="0x7f0b0062" />
    <public type="layout" name="popup_tip_vip" id="0x7f0b0063" />
    <public type="layout" name="qiehuz_taojin_activity_home" id="0x7f0b0064" />
    <public type="layout" name="select_dialog_item_material" id="0x7f0b0065" />
    <public type="layout" name="select_dialog_multichoice_material" id="0x7f0b0066" />
    <public type="layout" name="select_dialog_singlechoice_material" id="0x7f0b0067" />
    <public type="layout" name="support_simple_spinner_dropdown_item" id="0x7f0b0068" />
    <public type="layout" name="test_action_chip" id="0x7f0b0069" />
    <public type="layout" name="test_chip_zero_corner_radius" id="0x7f0b006a" />
    <public type="layout" name="test_design_checkbox" id="0x7f0b006b" />
    <public type="layout" name="test_design_radiobutton" id="0x7f0b006c" />
    <public type="layout" name="test_reflow_chipgroup" id="0x7f0b006d" />
    <public type="layout" name="test_toolbar" id="0x7f0b006e" />
    <public type="layout" name="test_toolbar_custom_background" id="0x7f0b006f" />
    <public type="layout" name="test_toolbar_elevation" id="0x7f0b0070" />
    <public type="layout" name="test_toolbar_surface" id="0x7f0b0071" />
    <public type="layout" name="text_view_with_line_height_from_appearance" id="0x7f0b0072" />
    <public type="layout" name="text_view_with_line_height_from_layout" id="0x7f0b0073" />
    <public type="layout" name="text_view_with_line_height_from_style" id="0x7f0b0074" />
    <public type="layout" name="text_view_with_theme_line_height" id="0x7f0b0075" />
    <public type="layout" name="text_view_without_line_height" id="0x7f0b0076" />
    <public type="layout" name="tianmu_activity_app_permissions" id="0x7f0b0077" />
    <public type="layout" name="tianmu_activity_detail" id="0x7f0b0078" />
    <public type="layout" name="tianmu_activity_download_list" id="0x7f0b0079" />
    <public type="layout" name="tianmu_activity_interstitial" id="0x7f0b007a" />
    <public type="layout" name="tianmu_activity_normal_web" id="0x7f0b007b" />
    <public type="layout" name="tianmu_activity_reward_root" id="0x7f0b007c" />
    <public type="layout" name="tianmu_activity_reward_vod" id="0x7f0b007d" />
    <public type="layout" name="tianmu_banner_template_style_left_pic" id="0x7f0b007e" />
    <public type="layout" name="tianmu_banner_template_style_pic" id="0x7f0b007f" />
    <public type="layout" name="tianmu_include_interaction_tips_view" id="0x7f0b0080" />
    <public type="layout" name="tianmu_include_interaction_tips_view2" id="0x7f0b0081" />
    <public type="layout" name="tianmu_include_normal_action_bar" id="0x7f0b0082" />
    <public type="layout" name="tianmu_include_reward_vod_action_bar" id="0x7f0b0083" />
    <public type="layout" name="tianmu_interstitial_app_info" id="0x7f0b0084" />
    <public type="layout" name="tianmu_interstitial_template_style_action_bar" id="0x7f0b0085" />
    <public type="layout" name="tianmu_interstitial_template_style_envelope" id="0x7f0b0086" />
    <public type="layout" name="tianmu_interstitial_template_style_envelope_paper" id="0x7f0b0087" />
    <public type="layout" name="tianmu_interstitial_template_style_pic" id="0x7f0b0088" />
    <public type="layout" name="tianmu_interstitial_template_style_pic_landscape" id="0x7f0b0089" />
    <public type="layout" name="tianmu_interstitial_template_style_top_pic" id="0x7f0b008a" />
    <public type="layout" name="tianmu_interstitial_template_style_top_pic_landscape" id="0x7f0b008b" />
    <public type="layout" name="tianmu_interstitial_template_style_video" id="0x7f0b008c" />
    <public type="layout" name="tianmu_interstitial_template_style_video_landscape" id="0x7f0b008d" />
    <public type="layout" name="tianmu_item_download_task" id="0x7f0b008e" />
    <public type="layout" name="tianmu_layout_download_status" id="0x7f0b008f" />
    <public type="layout" name="tianmu_layout_interstitial_end_card" id="0x7f0b0090" />
    <public type="layout" name="tianmu_layout_reward_detention_dialog" id="0x7f0b0091" />
    <public type="layout" name="tianmu_layout_reward_vod_dialog" id="0x7f0b0092" />
    <public type="layout" name="tianmu_native_template_style_bottom_pic_flow" id="0x7f0b0093" />
    <public type="layout" name="tianmu_native_template_style_left_pic_flow" id="0x7f0b0094" />
    <public type="layout" name="tianmu_native_template_style_pic_flow" id="0x7f0b0095" />
    <public type="layout" name="tianmu_native_template_style_right_pic_flow" id="0x7f0b0096" />
    <public type="layout" name="tianmu_native_template_style_top_pic_flow" id="0x7f0b0097" />
    <public type="layout" name="tianmu_notice_download_progress" id="0x7f0b0098" />
    <public type="layout" name="tianmu_notice_install_remind" id="0x7f0b0099" />
    <public type="layout" name="tianmu_splash_view" id="0x7f0b009a" />
    <public type="layout" name="tianmu_splash_view_hot_area" id="0x7f0b009b" />
    <public type="layout" name="tianmu_video_layout_complete_view" id="0x7f0b009c" />
    <public type="layout" name="tianmu_video_layout_error_view" id="0x7f0b009d" />
    <public type="layout" name="tianmu_video_layout_pause_view" id="0x7f0b009e" />
    <public type="layout" name="tianmu_video_layout_prepare_view" id="0x7f0b009f" />
    <public type="layout" name="tianmu_video_layout_standard_controller" id="0x7f0b00a0" />
    <public type="layout" name="tianmu_video_layout_video_status_view" id="0x7f0b00a1" />
    <public type="layout" name="tianmu_view_native_adapter_splash" id="0x7f0b00a2" />
    <public type="layout" name="tianmu_widget_auto_wake_up" id="0x7f0b00a3" />
    <public type="layout" name="tianmu_widget_give_polish" id="0x7f0b00a4" />
    <public type="layout" name="tianmu_widget_shake_view" id="0x7f0b00a5" />
    <public type="layout" name="tianmu_widget_skip_view" id="0x7f0b00a6" />
    <public type="layout" name="tianmu_widget_slide_animal_view" id="0x7f0b00a7" />
    <public type="layout" name="tianmu_widget_slide_circle_view" id="0x7f0b00a8" />
    <public type="layout" name="tianmu_widget_slide_four_direction_view" id="0x7f0b00a9" />
    <public type="layout" name="tianmu_widget_slide_view" id="0x7f0b00aa" />
    <public type="layout" name="tianmu_widget_splash_arc_view" id="0x7f0b00ab" />
    <public type="layout" name="tianmu_widget_swag_view" id="0x7f0b00ac" />
    <public type="layout" name="tianmu_widget_teetertotter_view" id="0x7f0b00ad" />
    <public type="layout" name="tianmu_widget_teetertotter_view_landscape" id="0x7f0b00ae" />
    <public type="layout" name="utils_toast_view" id="0x7f0b00af" />
    <public type="mipmap" name="aaa" id="0x7f0c0000" />
    <public type="mipmap" name="bg_remain_time" id="0x7f0c0001" />
    <public type="mipmap" name="bg_tip" id="0x7f0c0002" />
    <public type="mipmap" name="bg_tip_center" id="0x7f0c0003" />
    <public type="mipmap" name="btn_back_normal" id="0x7f0c0004" />
    <public type="mipmap" name="cd" id="0x7f0c0005" />
    <public type="mipmap" name="cd2" id="0x7f0c0006" />
    <public type="mipmap" name="cd3" id="0x7f0c0007" />
    <public type="mipmap" name="close2" id="0x7f0c0008" />
    <public type="mipmap" name="fenhong" id="0x7f0c0009" />
    <public type="mipmap" name="fenhong2" id="0x7f0c000a" />
    <public type="mipmap" name="ic_close" id="0x7f0c000b" />
    <public type="mipmap" name="ic_launcher" id="0x7f0c000c" />
    <public type="mipmap" name="ic_launcher_round" id="0x7f0c000d" />
    <public type="mipmap" name="ic_red" id="0x7f0c000e" />
    <public type="mipmap" name="ic_red_packet" id="0x7f0c000f" />
    <public type="mipmap" name="ic_tip_top" id="0x7f0c0010" />
    <public type="mipmap" name="ic_watch_reward" id="0x7f0c0011" />
    <public type="mipmap" name="img_back" id="0x7f0c0012" />
    <public type="mipmap" name="logo" id="0x7f0c0013" />
    <public type="mipmap" name="normal1" id="0x7f0c0014" />
    <public type="mipmap" name="redbg" id="0x7f0c0015" />
    <public type="mipmap" name="splash_image" id="0x7f0c0016" />
    <public type="plurals" name="mtrl_badge_content_description" id="0x7f0d0000" />
    <public type="string" name="abc_action_bar_home_description" id="0x7f0e0000" />
    <public type="string" name="abc_action_bar_up_description" id="0x7f0e0001" />
    <public type="string" name="abc_action_menu_overflow_description" id="0x7f0e0002" />
    <public type="string" name="abc_action_mode_done" id="0x7f0e0003" />
    <public type="string" name="abc_activity_chooser_view_see_all" id="0x7f0e0004" />
    <public type="string" name="abc_activitychooserview_choose_application" id="0x7f0e0005" />
    <public type="string" name="abc_capital_off" id="0x7f0e0006" />
    <public type="string" name="abc_capital_on" id="0x7f0e0007" />
    <public type="string" name="abc_menu_alt_shortcut_label" id="0x7f0e0008" />
    <public type="string" name="abc_menu_ctrl_shortcut_label" id="0x7f0e0009" />
    <public type="string" name="abc_menu_delete_shortcut_label" id="0x7f0e000a" />
    <public type="string" name="abc_menu_enter_shortcut_label" id="0x7f0e000b" />
    <public type="string" name="abc_menu_function_shortcut_label" id="0x7f0e000c" />
    <public type="string" name="abc_menu_meta_shortcut_label" id="0x7f0e000d" />
    <public type="string" name="abc_menu_shift_shortcut_label" id="0x7f0e000e" />
    <public type="string" name="abc_menu_space_shortcut_label" id="0x7f0e000f" />
    <public type="string" name="abc_menu_sym_shortcut_label" id="0x7f0e0010" />
    <public type="string" name="abc_prepend_shortcut_label" id="0x7f0e0011" />
    <public type="string" name="abc_search_hint" id="0x7f0e0012" />
    <public type="string" name="abc_searchview_description_clear" id="0x7f0e0013" />
    <public type="string" name="abc_searchview_description_query" id="0x7f0e0014" />
    <public type="string" name="abc_searchview_description_search" id="0x7f0e0015" />
    <public type="string" name="abc_searchview_description_submit" id="0x7f0e0016" />
    <public type="string" name="abc_searchview_description_voice" id="0x7f0e0017" />
    <public type="string" name="abc_shareactionprovider_share_with" id="0x7f0e0018" />
    <public type="string" name="abc_shareactionprovider_share_with_application" id="0x7f0e0019" />
    <public type="string" name="abc_toolbar_collapse_description" id="0x7f0e001a" />
    <public type="string" name="androidx_startup" id="0x7f0e001b" />
    <public type="string" name="app_load_mask" id="0x7f0e001c" />
    <public type="string" name="app_lz_sdk" id="0x7f0e001d" />
    <public type="string" name="app_name" id="0x7f0e001e" />
    <public type="string" name="app_update_warn" id="0x7f0e001f" />
    <public type="string" name="appbar_scrolling_view_behavior" id="0x7f0e0020" />
    <public type="string" name="basepopup_error_decorview" id="0x7f0e0021" />
    <public type="string" name="basepopup_error_destroyed" id="0x7f0e0022" />
    <public type="string" name="basepopup_error_non_act_context" id="0x7f0e0023" />
    <public type="string" name="basepopup_error_thread" id="0x7f0e0024" />
    <public type="string" name="basepopup_has_been_shown" id="0x7f0e0025" />
    <public type="string" name="basepopup_host" id="0x7f0e0026" />
    <public type="string" name="basepopup_host_destroyed" id="0x7f0e0027" />
    <public type="string" name="basepopup_shown_successful" id="0x7f0e0028" />
    <public type="string" name="basepopup_window_not_prepare" id="0x7f0e0029" />
    <public type="string" name="basepopup_window_prepared" id="0x7f0e002a" />
    <public type="string" name="bottom_sheet_behavior" id="0x7f0e002b" />
    <public type="string" name="character_counter_content_description" id="0x7f0e002c" />
    <public type="string" name="character_counter_overflowed_content_description" id="0x7f0e002d" />
    <public type="string" name="character_counter_pattern" id="0x7f0e002e" />
    <public type="string" name="chip_text" id="0x7f0e002f" />
    <public type="string" name="clear_text_end_icon_content_description" id="0x7f0e0030" />
    <public type="string" name="common_permission_access_media_location" id="0x7f0e0031" />
    <public type="string" name="common_permission_activity_recognition_api29" id="0x7f0e0032" />
    <public type="string" name="common_permission_activity_recognition_api30" id="0x7f0e0033" />
    <public type="string" name="common_permission_alarms_reminders" id="0x7f0e0034" />
    <public type="string" name="common_permission_alert" id="0x7f0e0035" />
    <public type="string" name="common_permission_all_file_access" id="0x7f0e0036" />
    <public type="string" name="common_permission_allow_notifications" id="0x7f0e0037" />
    <public type="string" name="common_permission_allow_notifications_access" id="0x7f0e0038" />
    <public type="string" name="common_permission_apps_with_usage_access" id="0x7f0e0039" />
    <public type="string" name="common_permission_background_default_option_label" id="0x7f0e003a" />
    <public type="string" name="common_permission_background_location_fail_hint" id="0x7f0e003b" />
    <public type="string" name="common_permission_background_sensors_fail_hint" id="0x7f0e003c" />
    <public type="string" name="common_permission_body_sensors" id="0x7f0e003d" />
    <public type="string" name="common_permission_body_sensors_background" id="0x7f0e003e" />
    <public type="string" name="common_permission_calendar" id="0x7f0e003f" />
    <public type="string" name="common_permission_call_logs" id="0x7f0e0040" />
    <public type="string" name="common_permission_camera" id="0x7f0e0041" />
    <public type="string" name="common_permission_colon" id="0x7f0e0042" />
    <public type="string" name="common_permission_comma" id="0x7f0e0043" />
    <public type="string" name="common_permission_contacts" id="0x7f0e0044" />
    <public type="string" name="common_permission_denied" id="0x7f0e0045" />
    <public type="string" name="common_permission_description_title" id="0x7f0e0046" />
    <public type="string" name="common_permission_display_over_other_apps" id="0x7f0e0047" />
    <public type="string" name="common_permission_do_not_disturb_access" id="0x7f0e0048" />
    <public type="string" name="common_permission_fail_assign_hint" id="0x7f0e0049" />
    <public type="string" name="common_permission_fail_hint" id="0x7f0e004a" />
    <public type="string" name="common_permission_get_installed_apps" id="0x7f0e004b" />
    <public type="string" name="common_permission_goto_setting_page" id="0x7f0e004c" />
    <public type="string" name="common_permission_granted" id="0x7f0e004d" />
    <public type="string" name="common_permission_ignore_battery_optimize" id="0x7f0e004e" />
    <public type="string" name="common_permission_image_and_video" id="0x7f0e004f" />
    <public type="string" name="common_permission_install_unknown_apps" id="0x7f0e0050" />
    <public type="string" name="common_permission_location" id="0x7f0e0051" />
    <public type="string" name="common_permission_location_background" id="0x7f0e0052" />
    <public type="string" name="common_permission_manual_assign_fail_background_location_hint" id="0x7f0e0053" />
    <public type="string" name="common_permission_manual_assign_fail_background_sensors_hint" id="0x7f0e0054" />
    <public type="string" name="common_permission_manual_assign_fail_hint" id="0x7f0e0055" />
    <public type="string" name="common_permission_manual_fail_hint" id="0x7f0e0056" />
    <public type="string" name="common_permission_media_location_hint_fail" id="0x7f0e0057" />
    <public type="string" name="common_permission_microphone" id="0x7f0e0058" />
    <public type="string" name="common_permission_modify_system_settings" id="0x7f0e0059" />
    <public type="string" name="common_permission_music_and_audio" id="0x7f0e005a" />
    <public type="string" name="common_permission_nearby_devices" id="0x7f0e005b" />
    <public type="string" name="common_permission_phone" id="0x7f0e005c" />
    <public type="string" name="common_permission_picture_in_picture" id="0x7f0e005d" />
    <public type="string" name="common_permission_post_notifications" id="0x7f0e005e" />
    <public type="string" name="common_permission_sms" id="0x7f0e005f" />
    <public type="string" name="common_permission_storage" id="0x7f0e0060" />
    <public type="string" name="common_permission_unknown" id="0x7f0e0061" />
    <public type="string" name="common_permission_vpn" id="0x7f0e0062" />
    <public type="string" name="default_filedownloader_notification_content" id="0x7f0e0063" />
    <public type="string" name="default_filedownloader_notification_title" id="0x7f0e0064" />
    <public type="string" name="error_icon_content_description" id="0x7f0e0065" />
    <public type="string" name="exposed_dropdown_menu_content_description" id="0x7f0e0066" />
    <public type="string" name="fab_transformation_scrim_behavior" id="0x7f0e0067" />
    <public type="string" name="fab_transformation_sheet_behavior" id="0x7f0e0068" />
    <public type="string" name="hide_bottom_view_on_scroll_behavior" id="0x7f0e0069" />
    <public type="string" name="icon_content_description" id="0x7f0e006a" />
    <public type="string" name="identifier_hiad_str_2" id="0x7f0e006b" />
    <public type="string" name="identifier_hiad_str_3" id="0x7f0e006c" />
    <public type="string" name="item_view_role_description" id="0x7f0e006d" />
    <public type="string" name="material_slider_range_end" id="0x7f0e006e" />
    <public type="string" name="material_slider_range_start" id="0x7f0e006f" />
    <public type="string" name="mtrl_badge_numberless_content_description" id="0x7f0e0070" />
    <public type="string" name="mtrl_chip_close_icon_content_description" id="0x7f0e0071" />
    <public type="string" name="mtrl_exceed_max_badge_number_content_description" id="0x7f0e0072" />
    <public type="string" name="mtrl_exceed_max_badge_number_suffix" id="0x7f0e0073" />
    <public type="string" name="mtrl_picker_a11y_next_month" id="0x7f0e0074" />
    <public type="string" name="mtrl_picker_a11y_prev_month" id="0x7f0e0075" />
    <public type="string" name="mtrl_picker_announce_current_selection" id="0x7f0e0076" />
    <public type="string" name="mtrl_picker_cancel" id="0x7f0e0077" />
    <public type="string" name="mtrl_picker_confirm" id="0x7f0e0078" />
    <public type="string" name="mtrl_picker_date_header_selected" id="0x7f0e0079" />
    <public type="string" name="mtrl_picker_date_header_title" id="0x7f0e007a" />
    <public type="string" name="mtrl_picker_date_header_unselected" id="0x7f0e007b" />
    <public type="string" name="mtrl_picker_day_of_week_column_header" id="0x7f0e007c" />
    <public type="string" name="mtrl_picker_invalid_format" id="0x7f0e007d" />
    <public type="string" name="mtrl_picker_invalid_format_example" id="0x7f0e007e" />
    <public type="string" name="mtrl_picker_invalid_format_use" id="0x7f0e007f" />
    <public type="string" name="mtrl_picker_invalid_range" id="0x7f0e0080" />
    <public type="string" name="mtrl_picker_navigate_to_year_description" id="0x7f0e0081" />
    <public type="string" name="mtrl_picker_out_of_range" id="0x7f0e0082" />
    <public type="string" name="mtrl_picker_range_header_only_end_selected" id="0x7f0e0083" />
    <public type="string" name="mtrl_picker_range_header_only_start_selected" id="0x7f0e0084" />
    <public type="string" name="mtrl_picker_range_header_selected" id="0x7f0e0085" />
    <public type="string" name="mtrl_picker_range_header_title" id="0x7f0e0086" />
    <public type="string" name="mtrl_picker_range_header_unselected" id="0x7f0e0087" />
    <public type="string" name="mtrl_picker_save" id="0x7f0e0088" />
    <public type="string" name="mtrl_picker_text_input_date_hint" id="0x7f0e0089" />
    <public type="string" name="mtrl_picker_text_input_date_range_end_hint" id="0x7f0e008a" />
    <public type="string" name="mtrl_picker_text_input_date_range_start_hint" id="0x7f0e008b" />
    <public type="string" name="mtrl_picker_text_input_day_abbr" id="0x7f0e008c" />
    <public type="string" name="mtrl_picker_text_input_month_abbr" id="0x7f0e008d" />
    <public type="string" name="mtrl_picker_text_input_year_abbr" id="0x7f0e008e" />
    <public type="string" name="mtrl_picker_toggle_to_calendar_input_mode" id="0x7f0e008f" />
    <public type="string" name="mtrl_picker_toggle_to_day_selection" id="0x7f0e0090" />
    <public type="string" name="mtrl_picker_toggle_to_text_input_mode" id="0x7f0e0091" />
    <public type="string" name="mtrl_picker_toggle_to_year_selection" id="0x7f0e0092" />
    <public type="string" name="password_toggle_content_description" id="0x7f0e0093" />
    <public type="string" name="path_password_eye" id="0x7f0e0094" />
    <public type="string" name="path_password_eye_mask_strike_through" id="0x7f0e0095" />
    <public type="string" name="path_password_eye_mask_visible" id="0x7f0e0096" />
    <public type="string" name="path_password_strike_through" id="0x7f0e0097" />
    <public type="string" name="search_menu_title" id="0x7f0e0098" />
    <public type="string" name="status_bar_notification_info_overflow" id="0x7f0e0099" />
    <public type="string" name="tianmu_app_detail" id="0x7f0e009a" />
    <public type="string" name="tianmu_app_do_not_remind" id="0x7f0e009b" />
    <public type="string" name="tianmu_app_install_now" id="0x7f0e009c" />
    <public type="string" name="tianmu_app_not_installed" id="0x7f0e009d" />
    <public type="string" name="tianmu_cancel" id="0x7f0e009e" />
    <public type="string" name="tianmu_confirm" id="0x7f0e009f" />
    <public type="string" name="tianmu_custom_ad_ad_target" id="0x7f0e00a0" />
    <public type="string" name="tianmu_custom_ad_application_permissions" id="0x7f0e00a1" />
    <public type="string" name="tianmu_custom_ad_check_details" id="0x7f0e00a2" />
    <public type="string" name="tianmu_custom_ad_content" id="0x7f0e00a3" />
    <public type="string" name="tianmu_custom_ad_download_give_up" id="0x7f0e00a4" />
    <public type="string" name="tianmu_custom_ad_download_now" id="0x7f0e00a5" />
    <public type="string" name="tianmu_custom_ad_download_now2" id="0x7f0e00a6" />
    <public type="string" name="tianmu_custom_ad_download_status_pause" id="0x7f0e00a7" />
    <public type="string" name="tianmu_custom_ad_download_status_start" id="0x7f0e00a8" />
    <public type="string" name="tianmu_custom_ad_platform_target" id="0x7f0e00a9" />
    <public type="string" name="tianmu_custom_ad_privacy_policy" id="0x7f0e00aa" />
    <public type="string" name="tianmu_custom_ad_title" id="0x7f0e00ab" />
    <public type="string" name="tianmu_custom_ad_video_continue_exit" id="0x7f0e00ac" />
    <public type="string" name="tianmu_custom_ad_video_keep_watch" id="0x7f0e00ad" />
    <public type="string" name="tianmu_dialog_notice_apply_message" id="0x7f0e00ae" />
    <public type="string" name="tianmu_dialog_notice_apply_title" id="0x7f0e00af" />
    <public type="string" name="tianmu_interaction_jump_tips" id="0x7f0e00b0" />
    <public type="string" name="tianmu_interaction_shake_the_phone" id="0x7f0e00b1" />
    <public type="string" name="tianmu_interaction_slide_up" id="0x7f0e00b2" />
    <public type="string" name="tianmu_interaction_slide_up2" id="0x7f0e00b3" />
    <public type="string" name="tianmu_interaction_turn_the_phone" id="0x7f0e00b4" />
    <public type="string" name="tianmu_interaction_turn_the_phone2" id="0x7f0e00b5" />
    <public type="string" name="tianmu_page_exception" id="0x7f0e00b6" />
    <public type="string" name="tianmu_page_exception_please_close" id="0x7f0e00b7" />
    <public type="string" name="tianmu_reward_achieve" id="0x7f0e00b8" />
    <public type="string" name="tianmu_reward_achieve_count_down" id="0x7f0e00b9" />
    <public type="string" name="tianmu_slide_to_learn_more" id="0x7f0e00ba" />
    <public type="string" name="tianmu_slide_to_right_check" id="0x7f0e00bb" />
    <public type="string" name="tianmu_slide_to_right_check2" id="0x7f0e00bc" />
    <public type="string" name="tianmu_slide_to_right_check3" id="0x7f0e00bd" />
    <public type="string" name="tianmu_slide_to_see_details" id="0x7f0e00be" />
    <public type="string" name="tianmu_video_continue_play" id="0x7f0e00bf" />
    <public type="string" name="tianmu_video_error_message" id="0x7f0e00c0" />
    <public type="string" name="tianmu_video_replay" id="0x7f0e00c1" />
    <public type="string" name="tianmu_video_retry" id="0x7f0e00c2" />
    <public type="string" name="tianmu_video_wifi_tip" id="0x7f0e00c3" />
    <public type="string" name="tianmu_wipe_to_see_details" id="0x7f0e00c4" />
    <public type="string" name="title_dashboard" id="0x7f0e00c5" />
    <public type="string" name="title_home" id="0x7f0e00c6" />
    <public type="string" name="title_ks_video" id="0x7f0e00c7" />
    <public type="string" name="title_notifications" id="0x7f0e00c8" />
    <public type="string" name="versionchecklib_cancel" id="0x7f0e00c9" />
    <public type="string" name="versionchecklib_check_new_version" id="0x7f0e00ca" />
    <public type="string" name="versionchecklib_confirm" id="0x7f0e00cb" />
    <public type="string" name="versionchecklib_download_apkname" id="0x7f0e00cc" />
    <public type="string" name="versionchecklib_download_fail" id="0x7f0e00cd" />
    <public type="string" name="versionchecklib_download_fail_retry" id="0x7f0e00ce" />
    <public type="string" name="versionchecklib_download_finish" id="0x7f0e00cf" />
    <public type="string" name="versionchecklib_download_progress" id="0x7f0e00d0" />
    <public type="string" name="versionchecklib_downloading" id="0x7f0e00d1" />
    <public type="string" name="versionchecklib_progress" id="0x7f0e00d2" />
    <public type="string" name="versionchecklib_retry" id="0x7f0e00d3" />
    <public type="string" name="versionchecklib_version_service_runing" id="0x7f0e00d4" />
    <public type="string" name="versionchecklib_write_permission_deny" id="0x7f0e00d5" />
    <public type="style" name="ActivityTranslucent" id="0x7f0f0000" />
    <public type="style" name="AlertDialog.AppCompat" id="0x7f0f0001" />
    <public type="style" name="AlertDialog.AppCompat.Light" id="0x7f0f0002" />
    <public type="style" name="AndroidThemeColorAccentYellow" id="0x7f0f0003" />
    <public type="style" name="Animation.AppCompat.Dialog" id="0x7f0f0004" />
    <public type="style" name="Animation.AppCompat.DropDownUp" id="0x7f0f0005" />
    <public type="style" name="Animation.AppCompat.Tooltip" id="0x7f0f0006" />
    <public type="style" name="Animation.Design.BottomSheetDialog" id="0x7f0f0007" />
    <public type="style" name="Animation.MaterialComponents.BottomSheetDialog" id="0x7f0f0008" />
    <public type="style" name="AppTheme" id="0x7f0f0009" />
    <public type="style" name="Base.AlertDialog.AppCompat" id="0x7f0f000a" />
    <public type="style" name="Base.AlertDialog.AppCompat.Light" id="0x7f0f000b" />
    <public type="style" name="Base.Animation.AppCompat.Dialog" id="0x7f0f000c" />
    <public type="style" name="Base.Animation.AppCompat.DropDownUp" id="0x7f0f000d" />
    <public type="style" name="Base.Animation.AppCompat.Tooltip" id="0x7f0f000e" />
    <public type="style" name="Base.CardView" id="0x7f0f000f" />
    <public type="style" name="Base.DialogWindowTitle.AppCompat" id="0x7f0f0010" />
    <public type="style" name="Base.DialogWindowTitleBackground.AppCompat" id="0x7f0f0011" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f0f0012" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f0f0013" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f0f0014" />
    <public type="style" name="Base.TextAppearance.AppCompat" id="0x7f0f0015" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body1" id="0x7f0f0016" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body2" id="0x7f0f0017" />
    <public type="style" name="Base.TextAppearance.AppCompat.Button" id="0x7f0f0018" />
    <public type="style" name="Base.TextAppearance.AppCompat.Caption" id="0x7f0f0019" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display1" id="0x7f0f001a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display2" id="0x7f0f001b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display3" id="0x7f0f001c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display4" id="0x7f0f001d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Headline" id="0x7f0f001e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Inverse" id="0x7f0f001f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large" id="0x7f0f0020" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large.Inverse" id="0x7f0f0021" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f0f0022" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f0f0023" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium" id="0x7f0f0024" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium.Inverse" id="0x7f0f0025" />
    <public type="style" name="Base.TextAppearance.AppCompat.Menu" id="0x7f0f0026" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult" id="0x7f0f0027" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f0f0028" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Title" id="0x7f0f0029" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small" id="0x7f0f002a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small.Inverse" id="0x7f0f002b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead" id="0x7f0f002c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead.Inverse" id="0x7f0f002d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title" id="0x7f0f002e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title.Inverse" id="0x7f0f002f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Tooltip" id="0x7f0f0030" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f0f0031" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f0f0032" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f0f0033" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f0f0034" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f0f0035" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f0f0036" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f0f0037" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button" id="0x7f0f0038" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f0f0039" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f0f003a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f0f003b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f0f003c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f0f003d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f0f003e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f0f003f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Switch" id="0x7f0f0040" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f0f0041" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Badge" id="0x7f0f0042" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Button" id="0x7f0f0043" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Headline6" id="0x7f0f0044" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Subtitle2" id="0x7f0f0045" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f0f0046" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f0f0047" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f0f0048" />
    <public type="style" name="Base.Theme.AppCompat" id="0x7f0f0049" />
    <public type="style" name="Base.Theme.AppCompat.CompactMenu" id="0x7f0f004a" />
    <public type="style" name="Base.Theme.AppCompat.Dialog" id="0x7f0f004b" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.Alert" id="0x7f0f004c" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.FixedSize" id="0x7f0f004d" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.MinWidth" id="0x7f0f004e" />
    <public type="style" name="Base.Theme.AppCompat.DialogWhenLarge" id="0x7f0f004f" />
    <public type="style" name="Base.Theme.AppCompat.Light" id="0x7f0f0050" />
    <public type="style" name="Base.Theme.AppCompat.Light.DarkActionBar" id="0x7f0f0051" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog" id="0x7f0f0052" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.Alert" id="0x7f0f0053" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.FixedSize" id="0x7f0f0054" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f0f0055" />
    <public type="style" name="Base.Theme.AppCompat.Light.DialogWhenLarge" id="0x7f0f0056" />
    <public type="style" name="Base.Theme.MaterialComponents" id="0x7f0f0057" />
    <public type="style" name="Base.Theme.MaterialComponents.Bridge" id="0x7f0f0058" />
    <public type="style" name="Base.Theme.MaterialComponents.CompactMenu" id="0x7f0f0059" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog" id="0x7f0f005a" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Alert" id="0x7f0f005b" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Bridge" id="0x7f0f005c" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.FixedSize" id="0x7f0f005d" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.MinWidth" id="0x7f0f005e" />
    <public type="style" name="Base.Theme.MaterialComponents.DialogWhenLarge" id="0x7f0f005f" />
    <public type="style" name="Base.Theme.MaterialComponents.Light" id="0x7f0f0060" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Bridge" id="0x7f0f0061" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar" id="0x7f0f0062" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f0f0063" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog" id="0x7f0f0064" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f0f0065" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f0f0066" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f0f0067" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f0f0068" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f0f0069" />
    <public type="style" name="Base.Theme.Wall" id="0x7f0f006a" />
    <public type="style" name="Base.ThemeOverlay.AppCompat" id="0x7f0f006b" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.ActionBar" id="0x7f0f006c" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark" id="0x7f0f006d" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f0f006e" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog" id="0x7f0f006f" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f0f0070" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Light" id="0x7f0f0071" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog" id="0x7f0f0072" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f0f0073" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" id="0x7f0f0074" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" id="0x7f0f0075" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f0f0076" />
    <public type="style" name="Base.V14.Theme.MaterialComponents" id="0x7f0f0077" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Bridge" id="0x7f0f0078" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog" id="0x7f0f0079" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog.Bridge" id="0x7f0f007a" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light" id="0x7f0f007b" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Bridge" id="0x7f0f007c" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f0f007d" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog" id="0x7f0f007e" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f0f007f" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" id="0x7f0f0080" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f0f0081" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f0f0082" />
    <public type="style" name="Base.V21.Theme.AppCompat" id="0x7f0f0083" />
    <public type="style" name="Base.V21.Theme.AppCompat.Dialog" id="0x7f0f0084" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light" id="0x7f0f0085" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light.Dialog" id="0x7f0f0086" />
    <public type="style" name="Base.V21.Theme.MaterialComponents" id="0x7f0f0087" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Dialog" id="0x7f0f0088" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Light" id="0x7f0f0089" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Light.Dialog" id="0x7f0f008a" />
    <public type="style" name="Base.V21.ThemeOverlay.AppCompat.Dialog" id="0x7f0f008b" />
    <public type="style" name="Base.V22.Theme.AppCompat" id="0x7f0f008c" />
    <public type="style" name="Base.V22.Theme.AppCompat.Light" id="0x7f0f008d" />
    <public type="style" name="Base.V23.Theme.AppCompat" id="0x7f0f008e" />
    <public type="style" name="Base.V23.Theme.AppCompat.Light" id="0x7f0f008f" />
    <public type="style" name="Base.V26.Theme.AppCompat" id="0x7f0f0090" />
    <public type="style" name="Base.V26.Theme.AppCompat.Light" id="0x7f0f0091" />
    <public type="style" name="Base.V26.Widget.AppCompat.Toolbar" id="0x7f0f0092" />
    <public type="style" name="Base.V28.Theme.AppCompat" id="0x7f0f0093" />
    <public type="style" name="Base.V28.Theme.AppCompat.Light" id="0x7f0f0094" />
    <public type="style" name="Base.V7.Theme.AppCompat" id="0x7f0f0095" />
    <public type="style" name="Base.V7.Theme.AppCompat.Dialog" id="0x7f0f0096" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light" id="0x7f0f0097" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light.Dialog" id="0x7f0f0098" />
    <public type="style" name="Base.V7.ThemeOverlay.AppCompat.Dialog" id="0x7f0f0099" />
    <public type="style" name="Base.V7.Widget.AppCompat.AutoCompleteTextView" id="0x7f0f009a" />
    <public type="style" name="Base.V7.Widget.AppCompat.EditText" id="0x7f0f009b" />
    <public type="style" name="Base.V7.Widget.AppCompat.Toolbar" id="0x7f0f009c" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar" id="0x7f0f009d" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.Solid" id="0x7f0f009e" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabBar" id="0x7f0f009f" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabText" id="0x7f0f00a0" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabView" id="0x7f0f00a1" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton" id="0x7f0f00a2" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.CloseMode" id="0x7f0f00a3" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.Overflow" id="0x7f0f00a4" />
    <public type="style" name="Base.Widget.AppCompat.ActionMode" id="0x7f0f00a5" />
    <public type="style" name="Base.Widget.AppCompat.ActivityChooserView" id="0x7f0f00a6" />
    <public type="style" name="Base.Widget.AppCompat.AutoCompleteTextView" id="0x7f0f00a7" />
    <public type="style" name="Base.Widget.AppCompat.Button" id="0x7f0f00a8" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless" id="0x7f0f00a9" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless.Colored" id="0x7f0f00aa" />
    <public type="style" name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f0f00ab" />
    <public type="style" name="Base.Widget.AppCompat.Button.Colored" id="0x7f0f00ac" />
    <public type="style" name="Base.Widget.AppCompat.Button.Small" id="0x7f0f00ad" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar" id="0x7f0f00ae" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f0f00af" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.CheckBox" id="0x7f0f00b0" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.RadioButton" id="0x7f0f00b1" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.Switch" id="0x7f0f00b2" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle" id="0x7f0f00b3" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle.Common" id="0x7f0f00b4" />
    <public type="style" name="Base.Widget.AppCompat.DropDownItem.Spinner" id="0x7f0f00b5" />
    <public type="style" name="Base.Widget.AppCompat.EditText" id="0x7f0f00b6" />
    <public type="style" name="Base.Widget.AppCompat.ImageButton" id="0x7f0f00b7" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar" id="0x7f0f00b8" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.Solid" id="0x7f0f00b9" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f0f00ba" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText" id="0x7f0f00bb" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f0f00bc" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabView" id="0x7f0f00bd" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu" id="0x7f0f00be" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f0f00bf" />
    <public type="style" name="Base.Widget.AppCompat.ListMenuView" id="0x7f0f00c0" />
    <public type="style" name="Base.Widget.AppCompat.ListPopupWindow" id="0x7f0f00c1" />
    <public type="style" name="Base.Widget.AppCompat.ListView" id="0x7f0f00c2" />
    <public type="style" name="Base.Widget.AppCompat.ListView.DropDown" id="0x7f0f00c3" />
    <public type="style" name="Base.Widget.AppCompat.ListView.Menu" id="0x7f0f00c4" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu" id="0x7f0f00c5" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu.Overflow" id="0x7f0f00c6" />
    <public type="style" name="Base.Widget.AppCompat.PopupWindow" id="0x7f0f00c7" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar" id="0x7f0f00c8" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar.Horizontal" id="0x7f0f00c9" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar" id="0x7f0f00ca" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Indicator" id="0x7f0f00cb" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Small" id="0x7f0f00cc" />
    <public type="style" name="Base.Widget.AppCompat.SearchView" id="0x7f0f00cd" />
    <public type="style" name="Base.Widget.AppCompat.SearchView.ActionBar" id="0x7f0f00ce" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar" id="0x7f0f00cf" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar.Discrete" id="0x7f0f00d0" />
    <public type="style" name="Base.Widget.AppCompat.Spinner" id="0x7f0f00d1" />
    <public type="style" name="Base.Widget.AppCompat.Spinner.Underlined" id="0x7f0f00d2" />
    <public type="style" name="Base.Widget.AppCompat.TextView" id="0x7f0f00d3" />
    <public type="style" name="Base.Widget.AppCompat.TextView.SpinnerItem" id="0x7f0f00d4" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar" id="0x7f0f00d5" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f0f00d6" />
    <public type="style" name="Base.Widget.Design.TabLayout" id="0x7f0f00d7" />
    <public type="style" name="Base.Widget.MaterialComponents.AutoCompleteTextView" id="0x7f0f00d8" />
    <public type="style" name="Base.Widget.MaterialComponents.CheckedTextView" id="0x7f0f00d9" />
    <public type="style" name="Base.Widget.MaterialComponents.Chip" id="0x7f0f00da" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu" id="0x7f0f00db" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f0f00dc" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f0f00dd" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f0f00de" />
    <public type="style" name="Base.Widget.MaterialComponents.Slider" id="0x7f0f00df" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputEditText" id="0x7f0f00e0" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputLayout" id="0x7f0f00e1" />
    <public type="style" name="Base.Widget.MaterialComponents.TextView" id="0x7f0f00e2" />
    <public type="style" name="CardView" id="0x7f0f00e3" />
    <public type="style" name="CardView.Dark" id="0x7f0f00e4" />
    <public type="style" name="CardView.Light" id="0x7f0f00e5" />
    <public type="style" name="EmptyTheme" id="0x7f0f00e6" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents" id="0x7f0f00e7" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Body.Text" id="0x7f0f00e8" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar" id="0x7f0f00e9" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner" id="0x7f0f00ea" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f0f00eb" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked" id="0x7f0f00ec" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f0f00ed" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked" id="0x7f0f00ee" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f0f00ef" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked" id="0x7f0f00f0" />
    <public type="style" name="Platform.AppCompat" id="0x7f0f00f1" />
    <public type="style" name="Platform.AppCompat.Light" id="0x7f0f00f2" />
    <public type="style" name="Platform.MaterialComponents" id="0x7f0f00f3" />
    <public type="style" name="Platform.MaterialComponents.Dialog" id="0x7f0f00f4" />
    <public type="style" name="Platform.MaterialComponents.Light" id="0x7f0f00f5" />
    <public type="style" name="Platform.MaterialComponents.Light.Dialog" id="0x7f0f00f6" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat" id="0x7f0f00f7" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Dark" id="0x7f0f00f8" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Light" id="0x7f0f00f9" />
    <public type="style" name="Platform.V21.AppCompat" id="0x7f0f00fa" />
    <public type="style" name="Platform.V21.AppCompat.Light" id="0x7f0f00fb" />
    <public type="style" name="Platform.V25.AppCompat" id="0x7f0f00fc" />
    <public type="style" name="Platform.V25.AppCompat.Light" id="0x7f0f00fd" />
    <public type="style" name="Platform.Widget.AppCompat.Spinner" id="0x7f0f00fe" />
    <public type="style" name="QieHZActivityTheme" id="0x7f0f00ff" />
    <public type="style" name="RtlOverlay.DialogWindowTitle.AppCompat" id="0x7f0f0100" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" id="0x7f0f0101" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" id="0x7f0f0102" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem" id="0x7f0f0103" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" id="0x7f0f0104" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" id="0x7f0f0105" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" id="0x7f0f0106" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" id="0x7f0f0107" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" id="0x7f0f0108" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown" id="0x7f0f0109" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" id="0x7f0f010a" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" id="0x7f0f010b" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" id="0x7f0f010c" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" id="0x7f0f010d" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" id="0x7f0f010e" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton" id="0x7f0f010f" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" id="0x7f0f0110" />
    <public type="style" name="ShapeAppearance.MaterialComponents" id="0x7f0f0111" />
    <public type="style" name="ShapeAppearance.MaterialComponents.LargeComponent" id="0x7f0f0112" />
    <public type="style" name="ShapeAppearance.MaterialComponents.MediumComponent" id="0x7f0f0113" />
    <public type="style" name="ShapeAppearance.MaterialComponents.SmallComponent" id="0x7f0f0114" />
    <public type="style" name="ShapeAppearance.MaterialComponents.Test" id="0x7f0f0115" />
    <public type="style" name="ShapeAppearance.MaterialComponents.Tooltip" id="0x7f0f0116" />
    <public type="style" name="ShapeAppearanceOverlay" id="0x7f0f0117" />
    <public type="style" name="ShapeAppearanceOverlay.BottomLeftDifferentCornerSize" id="0x7f0f0118" />
    <public type="style" name="ShapeAppearanceOverlay.BottomRightCut" id="0x7f0f0119" />
    <public type="style" name="ShapeAppearanceOverlay.Cut" id="0x7f0f011a" />
    <public type="style" name="ShapeAppearanceOverlay.DifferentCornerSize" id="0x7f0f011b" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.BottomSheet" id="0x7f0f011c" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.Chip" id="0x7f0f011d" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton" id="0x7f0f011e" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton" id="0x7f0f011f" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" id="0x7f0f0120" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen" id="0x7f0f0121" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year" id="0x7f0f0122" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox" id="0x7f0f0123" />
    <public type="style" name="ShapeAppearanceOverlay.TopLeftCut" id="0x7f0f0124" />
    <public type="style" name="ShapeAppearanceOverlay.TopRightDifferentCornerSize" id="0x7f0f0125" />
    <public type="style" name="Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" id="0x7f0f0126" />
    <public type="style" name="Test.Theme.MaterialComponents.MaterialCalendar" id="0x7f0f0127" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar" id="0x7f0f0128" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar.Day" id="0x7f0f0129" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected" id="0x7f0f012a" />
    <public type="style" name="TestStyleWithLineHeight" id="0x7f0f012b" />
    <public type="style" name="TestStyleWithLineHeightAppearance" id="0x7f0f012c" />
    <public type="style" name="TestStyleWithThemeLineHeightAttribute" id="0x7f0f012d" />
    <public type="style" name="TestStyleWithoutLineHeight" id="0x7f0f012e" />
    <public type="style" name="TestThemeWithLineHeight" id="0x7f0f012f" />
    <public type="style" name="TestThemeWithLineHeightDisabled" id="0x7f0f0130" />
    <public type="style" name="TextAppearance.AppCompat" id="0x7f0f0131" />
    <public type="style" name="TextAppearance.AppCompat.Body1" id="0x7f0f0132" />
    <public type="style" name="TextAppearance.AppCompat.Body2" id="0x7f0f0133" />
    <public type="style" name="TextAppearance.AppCompat.Button" id="0x7f0f0134" />
    <public type="style" name="TextAppearance.AppCompat.Caption" id="0x7f0f0135" />
    <public type="style" name="TextAppearance.AppCompat.Display1" id="0x7f0f0136" />
    <public type="style" name="TextAppearance.AppCompat.Display2" id="0x7f0f0137" />
    <public type="style" name="TextAppearance.AppCompat.Display3" id="0x7f0f0138" />
    <public type="style" name="TextAppearance.AppCompat.Display4" id="0x7f0f0139" />
    <public type="style" name="TextAppearance.AppCompat.Headline" id="0x7f0f013a" />
    <public type="style" name="TextAppearance.AppCompat.Inverse" id="0x7f0f013b" />
    <public type="style" name="TextAppearance.AppCompat.Large" id="0x7f0f013c" />
    <public type="style" name="TextAppearance.AppCompat.Large.Inverse" id="0x7f0f013d" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" id="0x7f0f013e" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Title" id="0x7f0f013f" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f0f0140" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f0f0141" />
    <public type="style" name="TextAppearance.AppCompat.Medium" id="0x7f0f0142" />
    <public type="style" name="TextAppearance.AppCompat.Medium.Inverse" id="0x7f0f0143" />
    <public type="style" name="TextAppearance.AppCompat.Menu" id="0x7f0f0144" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f0f0145" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Title" id="0x7f0f0146" />
    <public type="style" name="TextAppearance.AppCompat.Small" id="0x7f0f0147" />
    <public type="style" name="TextAppearance.AppCompat.Small.Inverse" id="0x7f0f0148" />
    <public type="style" name="TextAppearance.AppCompat.Subhead" id="0x7f0f0149" />
    <public type="style" name="TextAppearance.AppCompat.Subhead.Inverse" id="0x7f0f014a" />
    <public type="style" name="TextAppearance.AppCompat.Title" id="0x7f0f014b" />
    <public type="style" name="TextAppearance.AppCompat.Title.Inverse" id="0x7f0f014c" />
    <public type="style" name="TextAppearance.AppCompat.Tooltip" id="0x7f0f014d" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f0f014e" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f0f014f" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f0f0150" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f0f0151" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f0f0152" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f0f0153" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" id="0x7f0f0154" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f0f0155" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" id="0x7f0f0156" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button" id="0x7f0f0157" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f0f0158" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f0f0159" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f0f015a" />
    <public type="style" name="TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f0f015b" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f0f015c" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f0f015d" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f0f015e" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Switch" id="0x7f0f015f" />
    <public type="style" name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f0f0160" />
    <public type="style" name="TextAppearance.Compat.Notification" id="0x7f0f0161" />
    <public type="style" name="TextAppearance.Compat.Notification.Info" id="0x7f0f0162" />
    <public type="style" name="TextAppearance.Compat.Notification.Info.Media" id="0x7f0f0163" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2" id="0x7f0f0164" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2.Media" id="0x7f0f0165" />
    <public type="style" name="TextAppearance.Compat.Notification.Media" id="0x7f0f0166" />
    <public type="style" name="TextAppearance.Compat.Notification.Time" id="0x7f0f0167" />
    <public type="style" name="TextAppearance.Compat.Notification.Time.Media" id="0x7f0f0168" />
    <public type="style" name="TextAppearance.Compat.Notification.Title" id="0x7f0f0169" />
    <public type="style" name="TextAppearance.Compat.Notification.Title.Media" id="0x7f0f016a" />
    <public type="style" name="TextAppearance.Design.CollapsingToolbar.Expanded" id="0x7f0f016b" />
    <public type="style" name="TextAppearance.Design.Counter" id="0x7f0f016c" />
    <public type="style" name="TextAppearance.Design.Counter.Overflow" id="0x7f0f016d" />
    <public type="style" name="TextAppearance.Design.Error" id="0x7f0f016e" />
    <public type="style" name="TextAppearance.Design.HelperText" id="0x7f0f016f" />
    <public type="style" name="TextAppearance.Design.Hint" id="0x7f0f0170" />
    <public type="style" name="TextAppearance.Design.Placeholder" id="0x7f0f0171" />
    <public type="style" name="TextAppearance.Design.Prefix" id="0x7f0f0172" />
    <public type="style" name="TextAppearance.Design.Snackbar.Message" id="0x7f0f0173" />
    <public type="style" name="TextAppearance.Design.Suffix" id="0x7f0f0174" />
    <public type="style" name="TextAppearance.Design.Tab" id="0x7f0f0175" />
    <public type="style" name="TextAppearance.MaterialComponents.Badge" id="0x7f0f0176" />
    <public type="style" name="TextAppearance.MaterialComponents.Body1" id="0x7f0f0177" />
    <public type="style" name="TextAppearance.MaterialComponents.Body2" id="0x7f0f0178" />
    <public type="style" name="TextAppearance.MaterialComponents.Button" id="0x7f0f0179" />
    <public type="style" name="TextAppearance.MaterialComponents.Caption" id="0x7f0f017a" />
    <public type="style" name="TextAppearance.MaterialComponents.Chip" id="0x7f0f017b" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline1" id="0x7f0f017c" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline2" id="0x7f0f017d" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline3" id="0x7f0f017e" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline4" id="0x7f0f017f" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline5" id="0x7f0f0180" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline6" id="0x7f0f0181" />
    <public type="style" name="TextAppearance.MaterialComponents.Overline" id="0x7f0f0182" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle1" id="0x7f0f0183" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle2" id="0x7f0f0184" />
    <public type="style" name="TextAppearance.MaterialComponents.Tooltip" id="0x7f0f0185" />
    <public type="style" name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f0f0186" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f0f0187" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f0f0188" />
    <public type="style" name="Theme.AppCompat" id="0x7f0f0189" />
    <public type="style" name="Theme.AppCompat.CompactMenu" id="0x7f0f018a" />
    <public type="style" name="Theme.AppCompat.DayNight" id="0x7f0f018b" />
    <public type="style" name="Theme.AppCompat.DayNight.DarkActionBar" id="0x7f0f018c" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog" id="0x7f0f018d" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.Alert" id="0x7f0f018e" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.MinWidth" id="0x7f0f018f" />
    <public type="style" name="Theme.AppCompat.DayNight.DialogWhenLarge" id="0x7f0f0190" />
    <public type="style" name="Theme.AppCompat.DayNight.NoActionBar" id="0x7f0f0191" />
    <public type="style" name="Theme.AppCompat.Dialog" id="0x7f0f0192" />
    <public type="style" name="Theme.AppCompat.Dialog.Alert" id="0x7f0f0193" />
    <public type="style" name="Theme.AppCompat.Dialog.MinWidth" id="0x7f0f0194" />
    <public type="style" name="Theme.AppCompat.DialogWhenLarge" id="0x7f0f0195" />
    <public type="style" name="Theme.AppCompat.Empty" id="0x7f0f0196" />
    <public type="style" name="Theme.AppCompat.Light" id="0x7f0f0197" />
    <public type="style" name="Theme.AppCompat.Light.DarkActionBar" id="0x7f0f0198" />
    <public type="style" name="Theme.AppCompat.Light.Dialog" id="0x7f0f0199" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.Alert" id="0x7f0f019a" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f0f019b" />
    <public type="style" name="Theme.AppCompat.Light.DialogWhenLarge" id="0x7f0f019c" />
    <public type="style" name="Theme.AppCompat.Light.NoActionBar" id="0x7f0f019d" />
    <public type="style" name="Theme.AppCompat.NoActionBar" id="0x7f0f019e" />
    <public type="style" name="Theme.Design" id="0x7f0f019f" />
    <public type="style" name="Theme.Design.BottomSheetDialog" id="0x7f0f01a0" />
    <public type="style" name="Theme.Design.Light" id="0x7f0f01a1" />
    <public type="style" name="Theme.Design.Light.BottomSheetDialog" id="0x7f0f01a2" />
    <public type="style" name="Theme.Design.Light.NoActionBar" id="0x7f0f01a3" />
    <public type="style" name="Theme.Design.NoActionBar" id="0x7f0f01a4" />
    <public type="style" name="Theme.MaterialComponents" id="0x7f0f01a5" />
    <public type="style" name="Theme.MaterialComponents.BottomSheetDialog" id="0x7f0f01a6" />
    <public type="style" name="Theme.MaterialComponents.Bridge" id="0x7f0f01a7" />
    <public type="style" name="Theme.MaterialComponents.CompactMenu" id="0x7f0f01a8" />
    <public type="style" name="Theme.MaterialComponents.DayNight" id="0x7f0f01a9" />
    <public type="style" name="Theme.MaterialComponents.DayNight.BottomSheetDialog" id="0x7f0f01aa" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Bridge" id="0x7f0f01ab" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar" id="0x7f0f01ac" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" id="0x7f0f01ad" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog" id="0x7f0f01ae" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert" id="0x7f0f01af" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" id="0x7f0f01b0" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Bridge" id="0x7f0f01b1" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" id="0x7f0f01b2" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" id="0x7f0f01b3" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" id="0x7f0f01b4" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" id="0x7f0f01b5" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DialogWhenLarge" id="0x7f0f01b6" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar" id="0x7f0f01b7" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" id="0x7f0f01b8" />
    <public type="style" name="Theme.MaterialComponents.Dialog" id="0x7f0f01b9" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert" id="0x7f0f01ba" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert.Bridge" id="0x7f0f01bb" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Bridge" id="0x7f0f01bc" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize" id="0x7f0f01bd" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize.Bridge" id="0x7f0f01be" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth" id="0x7f0f01bf" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth.Bridge" id="0x7f0f01c0" />
    <public type="style" name="Theme.MaterialComponents.DialogWhenLarge" id="0x7f0f01c1" />
    <public type="style" name="Theme.MaterialComponents.Light" id="0x7f0f01c2" />
    <public type="style" name="Theme.MaterialComponents.Light.BarSize" id="0x7f0f01c3" />
    <public type="style" name="Theme.MaterialComponents.Light.BottomSheetDialog" id="0x7f0f01c4" />
    <public type="style" name="Theme.MaterialComponents.Light.Bridge" id="0x7f0f01c5" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar" id="0x7f0f01c6" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f0f01c7" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog" id="0x7f0f01c8" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f0f01c9" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert.Bridge" id="0x7f0f01ca" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f0f01cb" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f0f01cc" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge" id="0x7f0f01cd" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f0f01ce" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge" id="0x7f0f01cf" />
    <public type="style" name="Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f0f01d0" />
    <public type="style" name="Theme.MaterialComponents.Light.LargeTouch" id="0x7f0f01d1" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar" id="0x7f0f01d2" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar.Bridge" id="0x7f0f01d3" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar" id="0x7f0f01d4" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar.Bridge" id="0x7f0f01d5" />
    <public type="style" name="Theme.Wall" id="0x7f0f01d6" />
    <public type="style" name="ThemeOverlay.AppCompat" id="0x7f0f01d7" />
    <public type="style" name="ThemeOverlay.AppCompat.ActionBar" id="0x7f0f01d8" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark" id="0x7f0f01d9" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f0f01da" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight" id="0x7f0f01db" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight.ActionBar" id="0x7f0f01dc" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog" id="0x7f0f01dd" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f0f01de" />
    <public type="style" name="ThemeOverlay.AppCompat.Light" id="0x7f0f01df" />
    <public type="style" name="ThemeOverlay.Design.TextInputEditText" id="0x7f0f01e0" />
    <public type="style" name="ThemeOverlay.MaterialComponents" id="0x7f0f01e1" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar" id="0x7f0f01e2" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar.Primary" id="0x7f0f01e3" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar.Surface" id="0x7f0f01e4" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView" id="0x7f0f01e5" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f0f01e6" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" id="0x7f0f01e7" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f0f01e8" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f0f01e9" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomAppBar.Primary" id="0x7f0f01ea" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomAppBar.Surface" id="0x7f0f01eb" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomSheetDialog" id="0x7f0f01ec" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark" id="0x7f0f01ed" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark.ActionBar" id="0x7f0f01ee" />
    <public type="style" name="ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog" id="0x7f0f01ef" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog" id="0x7f0f01f0" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f0f01f1" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" id="0x7f0f01f2" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light" id="0x7f0f01f3" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light.BottomSheetDialog" id="0x7f0f01f4" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" id="0x7f0f01f5" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f0f01f6" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered" id="0x7f0f01f7" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date" id="0x7f0f01f8" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar" id="0x7f0f01f9" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text" id="0x7f0f01fa" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day" id="0x7f0f01fb" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner" id="0x7f0f01fc" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar" id="0x7f0f01fd" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f0f01fe" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText" id="0x7f0f01ff" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" id="0x7f0f0200" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f0f0201" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f0f0202" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f0f0203" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Toolbar.Primary" id="0x7f0f0204" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Toolbar.Surface" id="0x7f0f0205" />
    <public type="style" name="ThemeOverlayColorAccentRed" id="0x7f0f0206" />
    <public type="style" name="WWAppTheme" id="0x7f0f0207" />
    <public type="style" name="WWAppTheme.NoActionBar" id="0x7f0f0208" />
    <public type="style" name="Widget.AppCompat.ActionBar" id="0x7f0f0209" />
    <public type="style" name="Widget.AppCompat.ActionBar.Solid" id="0x7f0f020a" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabBar" id="0x7f0f020b" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabText" id="0x7f0f020c" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabView" id="0x7f0f020d" />
    <public type="style" name="Widget.AppCompat.ActionButton" id="0x7f0f020e" />
    <public type="style" name="Widget.AppCompat.ActionButton.CloseMode" id="0x7f0f020f" />
    <public type="style" name="Widget.AppCompat.ActionButton.Overflow" id="0x7f0f0210" />
    <public type="style" name="Widget.AppCompat.ActionMode" id="0x7f0f0211" />
    <public type="style" name="Widget.AppCompat.ActivityChooserView" id="0x7f0f0212" />
    <public type="style" name="Widget.AppCompat.AutoCompleteTextView" id="0x7f0f0213" />
    <public type="style" name="Widget.AppCompat.Button" id="0x7f0f0214" />
    <public type="style" name="Widget.AppCompat.Button.Borderless" id="0x7f0f0215" />
    <public type="style" name="Widget.AppCompat.Button.Borderless.Colored" id="0x7f0f0216" />
    <public type="style" name="Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f0f0217" />
    <public type="style" name="Widget.AppCompat.Button.Colored" id="0x7f0f0218" />
    <public type="style" name="Widget.AppCompat.Button.Small" id="0x7f0f0219" />
    <public type="style" name="Widget.AppCompat.ButtonBar" id="0x7f0f021a" />
    <public type="style" name="Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f0f021b" />
    <public type="style" name="Widget.AppCompat.CompoundButton.CheckBox" id="0x7f0f021c" />
    <public type="style" name="Widget.AppCompat.CompoundButton.RadioButton" id="0x7f0f021d" />
    <public type="style" name="Widget.AppCompat.CompoundButton.Switch" id="0x7f0f021e" />
    <public type="style" name="Widget.AppCompat.DrawerArrowToggle" id="0x7f0f021f" />
    <public type="style" name="Widget.AppCompat.DropDownItem.Spinner" id="0x7f0f0220" />
    <public type="style" name="Widget.AppCompat.EditText" id="0x7f0f0221" />
    <public type="style" name="Widget.AppCompat.ImageButton" id="0x7f0f0222" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar" id="0x7f0f0223" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid" id="0x7f0f0224" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" id="0x7f0f0225" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f0f0226" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" id="0x7f0f0227" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText" id="0x7f0f0228" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f0f0229" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView" id="0x7f0f022a" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" id="0x7f0f022b" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton" id="0x7f0f022c" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.CloseMode" id="0x7f0f022d" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.Overflow" id="0x7f0f022e" />
    <public type="style" name="Widget.AppCompat.Light.ActionMode.Inverse" id="0x7f0f022f" />
    <public type="style" name="Widget.AppCompat.Light.ActivityChooserView" id="0x7f0f0230" />
    <public type="style" name="Widget.AppCompat.Light.AutoCompleteTextView" id="0x7f0f0231" />
    <public type="style" name="Widget.AppCompat.Light.DropDownItem.Spinner" id="0x7f0f0232" />
    <public type="style" name="Widget.AppCompat.Light.ListPopupWindow" id="0x7f0f0233" />
    <public type="style" name="Widget.AppCompat.Light.ListView.DropDown" id="0x7f0f0234" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu" id="0x7f0f0235" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f0f0236" />
    <public type="style" name="Widget.AppCompat.Light.SearchView" id="0x7f0f0237" />
    <public type="style" name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" id="0x7f0f0238" />
    <public type="style" name="Widget.AppCompat.ListMenuView" id="0x7f0f0239" />
    <public type="style" name="Widget.AppCompat.ListPopupWindow" id="0x7f0f023a" />
    <public type="style" name="Widget.AppCompat.ListView" id="0x7f0f023b" />
    <public type="style" name="Widget.AppCompat.ListView.DropDown" id="0x7f0f023c" />
    <public type="style" name="Widget.AppCompat.ListView.Menu" id="0x7f0f023d" />
    <public type="style" name="Widget.AppCompat.PopupMenu" id="0x7f0f023e" />
    <public type="style" name="Widget.AppCompat.PopupMenu.Overflow" id="0x7f0f023f" />
    <public type="style" name="Widget.AppCompat.PopupWindow" id="0x7f0f0240" />
    <public type="style" name="Widget.AppCompat.ProgressBar" id="0x7f0f0241" />
    <public type="style" name="Widget.AppCompat.ProgressBar.Horizontal" id="0x7f0f0242" />
    <public type="style" name="Widget.AppCompat.RatingBar" id="0x7f0f0243" />
    <public type="style" name="Widget.AppCompat.RatingBar.Indicator" id="0x7f0f0244" />
    <public type="style" name="Widget.AppCompat.RatingBar.Small" id="0x7f0f0245" />
    <public type="style" name="Widget.AppCompat.SearchView" id="0x7f0f0246" />
    <public type="style" name="Widget.AppCompat.SearchView.ActionBar" id="0x7f0f0247" />
    <public type="style" name="Widget.AppCompat.SeekBar" id="0x7f0f0248" />
    <public type="style" name="Widget.AppCompat.SeekBar.Discrete" id="0x7f0f0249" />
    <public type="style" name="Widget.AppCompat.Spinner" id="0x7f0f024a" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown" id="0x7f0f024b" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown.ActionBar" id="0x7f0f024c" />
    <public type="style" name="Widget.AppCompat.Spinner.Underlined" id="0x7f0f024d" />
    <public type="style" name="Widget.AppCompat.TextView" id="0x7f0f024e" />
    <public type="style" name="Widget.AppCompat.TextView.SpinnerItem" id="0x7f0f024f" />
    <public type="style" name="Widget.AppCompat.Toolbar" id="0x7f0f0250" />
    <public type="style" name="Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f0f0251" />
    <public type="style" name="Widget.Compat.NotificationActionContainer" id="0x7f0f0252" />
    <public type="style" name="Widget.Compat.NotificationActionText" id="0x7f0f0253" />
    <public type="style" name="Widget.Design.AppBarLayout" id="0x7f0f0254" />
    <public type="style" name="Widget.Design.BottomNavigationView" id="0x7f0f0255" />
    <public type="style" name="Widget.Design.BottomSheet.Modal" id="0x7f0f0256" />
    <public type="style" name="Widget.Design.CollapsingToolbar" id="0x7f0f0257" />
    <public type="style" name="Widget.Design.FloatingActionButton" id="0x7f0f0258" />
    <public type="style" name="Widget.Design.NavigationView" id="0x7f0f0259" />
    <public type="style" name="Widget.Design.ScrimInsetsFrameLayout" id="0x7f0f025a" />
    <public type="style" name="Widget.Design.Snackbar" id="0x7f0f025b" />
    <public type="style" name="Widget.Design.TabLayout" id="0x7f0f025c" />
    <public type="style" name="Widget.Design.TextInputEditText" id="0x7f0f025d" />
    <public type="style" name="Widget.Design.TextInputLayout" id="0x7f0f025e" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Primary" id="0x7f0f025f" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.PrimarySurface" id="0x7f0f0260" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Solid" id="0x7f0f0261" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Surface" id="0x7f0f0262" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Primary" id="0x7f0f0263" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" id="0x7f0f0264" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Surface" id="0x7f0f0265" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f0f0266" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" id="0x7f0f0267" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f0f0268" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f0f0269" />
    <public type="style" name="Widget.MaterialComponents.Badge" id="0x7f0f026a" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar" id="0x7f0f026b" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.Colored" id="0x7f0f026c" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" id="0x7f0f026d" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView" id="0x7f0f026e" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.Colored" id="0x7f0f026f" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" id="0x7f0f0270" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet" id="0x7f0f0271" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet.Modal" id="0x7f0f0272" />
    <public type="style" name="Widget.MaterialComponents.Button" id="0x7f0f0273" />
    <public type="style" name="Widget.MaterialComponents.Button.Icon" id="0x7f0f0274" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton" id="0x7f0f0275" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton.Icon" id="0x7f0f0276" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton" id="0x7f0f0277" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog" id="0x7f0f0278" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Flush" id="0x7f0f0279" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" id="0x7f0f027a" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Icon" id="0x7f0f027b" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Snackbar" id="0x7f0f027c" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton" id="0x7f0f027d" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" id="0x7f0f027e" />
    <public type="style" name="Widget.MaterialComponents.CardView" id="0x7f0f027f" />
    <public type="style" name="Widget.MaterialComponents.CheckedTextView" id="0x7f0f0280" />
    <public type="style" name="Widget.MaterialComponents.Chip.Action" id="0x7f0f0281" />
    <public type="style" name="Widget.MaterialComponents.Chip.Choice" id="0x7f0f0282" />
    <public type="style" name="Widget.MaterialComponents.Chip.Entry" id="0x7f0f0283" />
    <public type="style" name="Widget.MaterialComponents.Chip.Filter" id="0x7f0f0284" />
    <public type="style" name="Widget.MaterialComponents.ChipGroup" id="0x7f0f0285" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.CheckBox" id="0x7f0f0286" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.RadioButton" id="0x7f0f0287" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.Switch" id="0x7f0f0288" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton" id="0x7f0f0289" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon" id="0x7f0f028a" />
    <public type="style" name="Widget.MaterialComponents.FloatingActionButton" id="0x7f0f028b" />
    <public type="style" name="Widget.MaterialComponents.Light.ActionBar.Solid" id="0x7f0f028c" />
    <public type="style" name="Widget.MaterialComponents.MaterialButtonToggleGroup" id="0x7f0f028d" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar" id="0x7f0f028e" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day" id="0x7f0f028f" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Invalid" id="0x7f0f0290" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Selected" id="0x7f0f0291" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Today" id="0x7f0f0292" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.DayTextView" id="0x7f0f0293" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f0f0294" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton" id="0x7f0f0295" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderDivider" id="0x7f0f0296" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout" id="0x7f0f0297" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection" id="0x7f0f0298" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen" id="0x7f0f0299" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderTitle" id="0x7f0f029a" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" id="0x7f0f029b" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Item" id="0x7f0f029c" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year" id="0x7f0f029d" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Selected" id="0x7f0f029e" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Today" id="0x7f0f029f" />
    <public type="style" name="Widget.MaterialComponents.NavigationView" id="0x7f0f02a0" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu" id="0x7f0f02a1" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f0f02a2" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f0f02a3" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f0f02a4" />
    <public type="style" name="Widget.MaterialComponents.ShapeableImageView" id="0x7f0f02a5" />
    <public type="style" name="Widget.MaterialComponents.Slider" id="0x7f0f02a6" />
    <public type="style" name="Widget.MaterialComponents.Snackbar" id="0x7f0f02a7" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.FullWidth" id="0x7f0f02a8" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.TextView" id="0x7f0f02a9" />
    <public type="style" name="Widget.MaterialComponents.TabLayout" id="0x7f0f02aa" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.Colored" id="0x7f0f02ab" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.PrimarySurface" id="0x7f0f02ac" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox" id="0x7f0f02ad" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f0f02ae" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f0f02af" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f0f02b0" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox" id="0x7f0f02b1" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" id="0x7f0f02b2" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu" id="0x7f0f02b3" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu" id="0x7f0f02b4" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" id="0x7f0f02b5" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" id="0x7f0f02b6" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu" id="0x7f0f02b7" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu" id="0x7f0f02b8" />
    <public type="style" name="Widget.MaterialComponents.TextView" id="0x7f0f02b9" />
    <public type="style" name="Widget.MaterialComponents.Toolbar" id="0x7f0f02ba" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.Primary" id="0x7f0f02bb" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.PrimarySurface" id="0x7f0f02bc" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.Surface" id="0x7f0f02bd" />
    <public type="style" name="Widget.MaterialComponents.Tooltip" id="0x7f0f02be" />
    <public type="style" name="Widget.Support.CoordinatorLayout" id="0x7f0f02bf" />
    <public type="style" name="activityAnimation" id="0x7f0f02c0" />
    <public type="style" name="tianmu_DownloadConfirmDialogAnimationRight" id="0x7f0f02c1" />
    <public type="style" name="tianmu_DownloadConfirmDialogAnimationUp" id="0x7f0f02c2" />
    <public type="style" name="tianmu_DownloadConfirmDialogFullScreen" id="0x7f0f02c3" />
    <public type="style" name="tianmu_ad_detail_activity" id="0x7f0f02c4" />
    <public type="style" name="tianmu_ad_source_base_style" id="0x7f0f02c5" />
    <public type="style" name="tianmu_ad_target_base_style" id="0x7f0f02c6" />
    <public type="style" name="tianmu_alpha_enter_exit" id="0x7f0f02c7" />
    <public type="style" name="tianmu_common_dialog" id="0x7f0f02c8" />
    <public type="style" name="tianmu_interstitial_activity" id="0x7f0f02c9" />
    <public type="style" name="tianmu_iv_video_mute_style" id="0x7f0f02ca" />
    <public type="style" name="tianmu_notice_progress_bar" id="0x7f0f02cb" />
    <public type="style" name="tianmu_reward_common_dialog" id="0x7f0f02cc" />
    <public type="style" name="tianmu_translucent_activity" id="0x7f0f02cd" />
    <public type="style" name="tianmu_video_no_status" id="0x7f0f02ce" />
    <public type="style" name="tianmu_widget_interaction_tips_style" id="0x7f0f02cf" />
    <public type="style" name="tianmu_widget_reward_action_bar_btn" id="0x7f0f02d0" />
    <public type="style" name="versionCheckLibvtransparentTheme" id="0x7f0f02d1" />
    <public type="style" name="versioncheckLibAppTheme" id="0x7f0f02d2" />
    <public type="xml" name="anythink_bk_gdt_file_path" id="0x7f110000" />
    <public type="xml" name="anythink_bk_sigmob_file_path" id="0x7f110001" />
    <public type="xml" name="anythink_bk_tt_file_path" id="0x7f110002" />
    <public type="xml" name="file_path" id="0x7f110003" />
    <public type="xml" name="file_provider" id="0x7f110004" />
    <public type="xml" name="hr_file_path" id="0x7f110005" />
    <public type="xml" name="locales_config" id="0x7f110006" />
    <public type="xml" name="mb_provider_paths" id="0x7f110007" />
    <public type="xml" name="network_config" id="0x7f110008" />
    <public type="xml" name="network_security_config" id="0x7f110009" />
    <public type="xml" name="standalone_badge" id="0x7f11000a" />
    <public type="xml" name="standalone_badge_gravity_bottom_end" id="0x7f11000b" />
    <public type="xml" name="standalone_badge_gravity_bottom_start" id="0x7f11000c" />
    <public type="xml" name="standalone_badge_gravity_top_start" id="0x7f11000d" />
    <public type="xml" name="standalone_badge_offset" id="0x7f11000e" />
    <public type="xml" name="tianmu_file_paths" id="0x7f11000f" />
    <public type="xml" name="util_code_provider_paths" id="0x7f110010" />
    <public type="xml" name="versionchecklib_file_paths" id="0x7f110011" />
    <public type="xml" name="ww_paths" id="0x7f110012" />
    <public type="xml" name="xw_paths" id="0x7f110013" />
</resources>
