<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:id="@+id/tianmu_ll_splash_ad_content"
        android:background="@drawable/tianmu_bg_native_adapter_splash"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="185dp"
        android:layout_marginRight="20dp"
        android:elevation="6dp">
        <RelativeLayout
            android:id="@+id/tianmu_rl_splash_container"
            android:layout_width="match_parent"
            android:layout_height="176dp"
            android:layout_marginLeft="18dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="18dp">
            <com.tianmu.biz.widget.AdVideoView
                android:id="@+id/tianmu_splash_video_view"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
            <com.tianmu.biz.widget.roundimage.RoundedImageView
                android:id="@+id/tianmu_iv_splash_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"/>
        </RelativeLayout>
        <TextView
            android:textSize="18sp"
            android:textColor="#000000"
            android:ellipsize="end"
            android:gravity="center_horizontal"
            android:id="@+id/tianmu_tv_splash_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="18dp"
            android:layout_marginTop="24dp"
            android:layout_marginRight="18dp"
            android:layout_marginBottom="15dp"
            android:maxLines="3"/>
    </LinearLayout>
</RelativeLayout>
