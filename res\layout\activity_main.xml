<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:background="#ffffff"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ProgressBar
            android:id="@+id/progress_bar"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="3dp"
            android:max="100"
            android:progressDrawable="@drawable/progressbar_horizontal"
            style="?android:attr/progressBarStyleHorizontal"/>
        <com.tencent.smtt.sdk.WebView
            android:id="@+id/web_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </LinearLayout>
    <FrameLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <FrameLayout
        android:id="@+id/fl_load_mask"
        android:background="#fff"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <FrameLayout
        android:id="@+id/fl_update_warn"
        android:background="#cc000000"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="24sp"
            android:textColor="#ffffff"
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_update_warn"/>
    </FrameLayout>
    <FrameLayout
        android:id="@+id/load_box"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@mipmap/splash_image"
            android:scaleType="fitXY"/>
        <ProgressBar
            android:layout_gravity="bottom"
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:layout_marginBottom="100dp"
            android:max="100"
            android:progress="0"
            android:progressTint="#fff"
            android:progressBackgroundTint="#aaa"
            style="@android:style/Widget.ProgressBar.Horizontal"/>
    </FrameLayout>
</RelativeLayout>
