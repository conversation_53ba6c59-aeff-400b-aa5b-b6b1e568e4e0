<?xml version="1.0" encoding="utf-8"?>
<inset xmlns:android="http://schemas.android.com/apk/res/android"
    android:insetLeft="@dimen/abc_edit_text_inset_horizontal_material"
    android:insetRight="@dimen/abc_edit_text_inset_horizontal_material"
    android:insetTop="@dimen/abc_edit_text_inset_top_material"
    android:insetBottom="@dimen/abc_edit_text_inset_bottom_material">
    <selector>
        <item android:state_enabled="false">
            <nine-patch
                android:src="@drawable/abc_textfield_default_mtrl_alpha"
                android:tint="?attr/colorControlNormal"
                android:alpha="?android:attr/disabledAlpha"/>
        </item>
        <item
            android:state_focused="false"
            android:state_pressed="false">
            <nine-patch
                android:src="@drawable/abc_textfield_default_mtrl_alpha"
                android:tint="?attr/colorControlNormal"/>
        </item>
        <item>
            <nine-patch
                android:src="@drawable/abc_textfield_activated_mtrl_alpha"
                android:tint="?attr/colorControlActivated"/>
        </item>
    </selector>
</inset>
