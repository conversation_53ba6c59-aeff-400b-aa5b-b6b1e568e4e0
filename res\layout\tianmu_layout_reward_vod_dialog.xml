<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tianmu_library_rl_cover"
    android:background="#00000000"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:id="@+id/tianmu_library_view_gradient_start"
        android:background="@drawable/tianmu_bg_reward_action_bar_gradient_start"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:layout_alignTop="@+id/tianmu_library_ll_ad_content"/>
    <View
        android:id="@+id/tianmu_library_view_gradient_end"
        android:background="@drawable/tianmu_bg_reward_action_bar_gradient_end"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tianmu_library_view_gradient_start"
        android:layout_alignParentBottom="true"/>
    <FrameLayout
        android:id="@+id/tianmu_library_fl_click"
        android:background="#63000000"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <ImageView
        android:id="@+id/tianmu_library_iv_close"
        android:background="@drawable/tianmu_shape_75cccccc_circle"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginTop="70dp"
        android:layout_marginRight="24dp"
        android:src="@drawable/tianmu_reward_close"
        android:layout_alignParentRight="true"/>
    <RelativeLayout
        android:id="@+id/tianmu_library_ll_ad_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">
        <LinearLayout
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="343dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp">
            <TextView
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#f8f2f2"
                android:gravity="center"
                android:id="@+id/tianmu_library_tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="64dp"
                android:layout_marginRight="16dp"
                android:text="@string/tianmu_custom_ad_title"
                android:singleLine="true"/>
            <TextView
                android:textSize="14sp"
                android:textColor="#faf7f7"
                android:gravity="center"
                android:id="@+id/tianmu_library_tv_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="32dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="60dp"
                android:text="@string/tianmu_custom_ad_title"/>
        </LinearLayout>
        <com.tianmu.biz.widget.roundimage.RoundedImageView
            android:id="@+id/tianmu_library_iv_app_icon"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:scaleType="centerCrop"
            android:layout_centerHorizontal="true"/>
    </RelativeLayout>
    <TextView
        android:id="@+id/tianmu_library_tv_btn_function_bg"
        android:background="@drawable/tianmu_shape_ff7c9eb9_radius36"
        android:layout_height="42dp"
        android:layout_below="@+id/tianmu_library_ll_ad_content"
        style="@style/tianmu_widget_reward_action_bar_btn"/>
    <TextView
        android:id="@+id/tianmu_library_tv_btn_function"
        android:background="@drawable/tianmu_shape_ff3790ef_radius36"
        android:layout_height="42dp"
        android:layout_below="@+id/tianmu_library_ll_ad_content"
        style="@style/tianmu_widget_reward_action_bar_btn"/>
    <TextView
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#ffffff"
        android:gravity="center"
        android:id="@+id/tianmu_library_tv_function"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/tianmu_library_tv_btn_function"
        android:layout_alignTop="@+id/tianmu_library_tv_btn_function"
        android:layout_alignRight="@+id/tianmu_library_tv_btn_function"
        android:layout_alignBottom="@+id/tianmu_library_tv_btn_function"/>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/tianmu_library_ll_target"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="54dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true">
        <TextView
            android:textColor="#b0ffffff"
            android:id="@+id/tianmu_tv_ad_target"
            android:background="@null"
            style="@style/tianmu_ad_target_base_style"/>
        <TextView
            android:textColor="#b0ffffff"
            android:id="@+id/tianmu_tv_ad_source"
            android:background="@null"
            android:visibility="gone"
            style="@style/tianmu_ad_target_base_style"/>
    </LinearLayout>
    <TextView
        android:textSize="8sp"
        android:textColor="#a3ffffff"
        android:id="@+id/tianmu_library_tv_app_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="17dp"
        android:layout_marginRight="12dp"
        android:layout_marginBottom="52dp"
        android:layout_toLeftOf="@+id/tianmu_library_ll_target"
        android:layout_alignParentBottom="true"/>
</RelativeLayout>
