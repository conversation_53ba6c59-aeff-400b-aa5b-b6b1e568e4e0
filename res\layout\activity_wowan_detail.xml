<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <FrameLayout
            android:id="@+id/frameLayoutId_detail"
            android:background="#f7f7f7"
            android:layout_width="match_parent"
            android:layout_height="45dp">
            <ImageView
                android:id="@+id/top_back_detail"
                android:background="#00000000"
                android:padding="10dp"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:src="@mipmap/btn_back_normal"/>
            <TextView
                android:textSize="15.5dp"
                android:textColor="#2d2d2d"
                android:layout_gravity="center"
                android:id="@+id/tv_wowan_title_detail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""/>
        </FrameLayout>
        <View
            android:id="@+id/viewId_detail"
            android:background="#d4d4d4"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/main_srl_detail"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <WebView
                android:id="@+id/webview_detail"
                android:scrollbars="none"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </LinearLayout>
    <RelativeLayout
        android:id="@+id/rl_loading"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ProgressBar
            android:id="@+id/pro_webview"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:layout_marginTop="45dp"
            android:max="100"
            android:progress="0"
            android:secondaryProgress="0"
            android:progressDrawable="@drawable/pro_wowan_webview_detail"
            style="?android:attr/progressBarStyleHorizontal"/>
    </RelativeLayout>
</RelativeLayout>
