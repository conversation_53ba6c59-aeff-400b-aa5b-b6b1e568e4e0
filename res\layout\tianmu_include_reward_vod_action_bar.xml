<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/tianmu_library_rl_ad_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:id="@+id/tianmu_library_gradient_start"
        android:background="@drawable/tianmu_bg_reward_action_bar_gradient_start"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_above="@+id/tianmu_library_gradient_end"/>
    <View
        android:id="@+id/tianmu_library_gradient_end"
        android:background="@drawable/tianmu_bg_reward_action_bar_gradient_end"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:layout_alignParentBottom="true"/>
    <com.tianmu.biz.widget.roundimage.RoundedImageView
        android:id="@+id/tianmu_library_iv_image"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="30dp"
        android:scaleType="centerCrop"
        android:layout_alignTop="@+id/tianmu_library_gradient_start"/>
    <TextView
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#ffffff"
        android:id="@+id/tianmu_library_tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:singleLine="true"
        android:layout_toLeftOf="@+id/tianmu_library_ll_target"
        android:layout_toRightOf="@+id/tianmu_library_iv_image"
        android:layout_alignTop="@+id/tianmu_library_iv_image"/>
    <TextView
        android:textSize="12sp"
        android:textColor="#b2f7f2f2"
        android:id="@+id/tianmu_library_tv_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:singleLine="true"
        android:layout_toLeftOf="@+id/tianmu_library_ll_target"
        android:layout_toRightOf="@+id/tianmu_library_iv_image"
        android:layout_alignBottom="@+id/tianmu_library_iv_image"/>
    <LinearLayout
        android:gravity="center"
        android:orientation="vertical"
        android:id="@+id/tianmu_library_ll_target"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="10dp"
        android:layout_alignTop="@+id/tianmu_library_iv_image"
        android:layout_alignBottom="@+id/tianmu_library_iv_image"
        android:layout_alignParentRight="true">
        <TextView
            android:id="@+id/tianmu_library_tv_ad_target"
            android:background="@null"
            style="@style/tianmu_ad_target_base_style"/>
        <TextView
            android:id="@+id/tianmu_library_tv_ad_source"
            android:background="@null"
            android:visibility="visible"
            style="@style/tianmu_ad_source_base_style"/>
    </LinearLayout>
    <TextView
        android:id="@+id/tianmu_library_tv_action_bg"
        android:background="@drawable/tianmu_shape_ff7c9eb9_radius36"
        android:layout_below="@+id/tianmu_library_iv_image"
        style="@style/tianmu_widget_reward_action_bar_btn"/>
    <TextView
        android:id="@+id/tianmu_library_tv_action"
        android:background="@drawable/tianmu_shape_ff3790ef_radius36"
        android:layout_below="@+id/tianmu_library_iv_image"
        style="@style/tianmu_widget_reward_action_bar_btn"/>
    <TextView
        android:textSize="15sp"
        android:textStyle="bold"
        android:textColor="#ffffff"
        android:ellipsize="end"
        android:gravity="center"
        android:id="@+id/tianmu_library_tv_action_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tianmu_custom_ad_download_now"
        android:layout_alignLeft="@+id/tianmu_library_tv_action"
        android:layout_alignTop="@+id/tianmu_library_tv_action"
        android:layout_alignRight="@+id/tianmu_library_tv_action"
        android:layout_alignBottom="@+id/tianmu_library_tv_action"/>
    <TextView
        android:textSize="8sp"
        android:textColor="#a3ffffff"
        android:id="@+id/tianmu_library_tv_app_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="17dp"
        android:layout_marginRight="16dp"
        android:layout_below="@+id/tianmu_library_tv_action"/>
    <ImageView
        android:id="@+id/tianmu_library_iv_small_interaction"
        android:background="@drawable/tianmu_shape_sway_perfect_circle_bg"
        android:visibility="invisible"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginRight="10dp"
        android:layout_above="@+id/tianmu_library_gradient_start"
        android:layout_alignParentRight="true"/>
</RelativeLayout>
