<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tianmu_rl_ad_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:id="@+id/tianmu_rl_inner_ad_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="0.5dp">
        <ImageView
            android:id="@+id/tianmu_iv_image"
            android:layout_width="170dp"
            android:layout_height="74dp"
            android:scaleType="centerCrop"
            android:layout_alignParentRight="true"/>
        <TextView
            android:textSize="16sp"
            android:textColor="#ff333333"
            android:ellipsize="end"
            android:id="@+id/tianmu_tv_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="12dp"
            android:maxLines="2"
            android:layout_toLeftOf="@+id/tianmu_iv_image"
            android:layout_alignTop="@+id/tianmu_iv_image"/>
        <TextView
            android:textSize="12sp"
            android:textColor="#ffb3b3b3"
            android:ellipsize="end"
            android:id="@+id/tianmu_tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="12dp"
            android:maxLines="1"
            android:layout_toLeftOf="@+id/tianmu_iv_close"
            android:layout_alignBottom="@+id/tianmu_iv_image"/>
        <TextView
            android:id="@+id/tianmu_tv_ad_target"
            android:layout_alignRight="@+id/tianmu_iv_image"
            android:layout_alignBottom="@+id/tianmu_iv_close"
            style="@style/tianmu_ad_target_base_style"/>
        <TextView
            android:id="@+id/tianmu_banner_tv_ad_source"
            android:layout_alignLeft="@+id/tianmu_iv_image"
            android:layout_alignTop="@+id/tianmu_iv_image"
            style="@style/tianmu_ad_source_base_style"/>
        <ImageView
            android:id="@+id/tianmu_iv_close"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginRight="6dp"
            android:src="@drawable/tianmu_icon_rec_round_close"
            android:scaleType="centerCrop"
            android:layout_toLeftOf="@+id/tianmu_iv_image"
            android:layout_alignBottom="@+id/tianmu_iv_image"/>
    </RelativeLayout>
</RelativeLayout>
