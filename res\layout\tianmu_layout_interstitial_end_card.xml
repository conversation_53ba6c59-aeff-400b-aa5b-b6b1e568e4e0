<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tianmu_library_rl_cover"
    android:background="#00000000"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:id="@+id/tianmu_library_fl_click"
        android:background="#cc000000"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <LinearLayout
        android:gravity="center"
        android:orientation="vertical"
        android:id="@+id/tianmu_library_ll_ad_content"
        android:layout_width="343dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">
        <TextView
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="#dddddd"
            android:gravity="center"
            android:id="@+id/tianmu_library_tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="24dp"
            android:layout_marginRight="16dp"
            android:text="@string/tianmu_custom_ad_title"
            android:singleLine="true"/>
        <TextView
            android:textSize="14sp"
            android:textColor="#dddddd"
            android:gravity="center"
            android:id="@+id/tianmu_library_tv_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginRight="16dp"
            android:text="@string/tianmu_custom_ad_content"/>
        <TextView
            android:textSize="16sp"
            android:textColor="#ffffff"
            android:id="@+id/tianmu_library_tv_function"
            android:background="@drawable/tianmu_shape_fa6400_radius4"
            android:paddingLeft="56dp"
            android:paddingTop="12dp"
            android:paddingRight="56dp"
            android:paddingBottom="12dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56dp"
            android:layout_marginBottom="48dp"
            android:text="@string/tianmu_custom_ad_download_now"/>
    </LinearLayout>
    <TextView
        android:id="@+id/tianmu_tv_ad_target"
        android:layout_margin="20dp"
        android:layout_alignBottom="@+id/tianmu_library_fl_click"
        style="@style/tianmu_ad_target_base_style"/>
</RelativeLayout>
