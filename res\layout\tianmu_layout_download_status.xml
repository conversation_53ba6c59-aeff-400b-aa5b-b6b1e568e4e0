<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="#ffffff"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/tianmu_library_tv_ad_btn_container">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <com.tianmu.biz.widget.roundimage.RoundedImageView
                android:id="@+id/tianmu_library_iv_ad_icon"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_marginTop="32dp"
                android:scaleType="centerCrop"
                android:layout_centerHorizontal="true"/>
            <TextView
                android:textSize="23sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:gravity="center"
                android:id="@+id/tianmu_library_tv_ad_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="22dp"
                android:maxLines="1"
                android:layout_below="@+id/tianmu_library_iv_ad_icon"/>
            <TextView
                android:textSize="14sp"
                android:textColor="#999999"
                android:ellipsize="end"
                android:gravity="center"
                android:id="@+id/tianmu_library_tv_ad_desc"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="18dp"
                android:layout_marginBottom="26dp"
                android:maxLines="2"
                android:layout_below="@+id/tianmu_library_tv_ad_title"/>
            <TextView
                android:textSize="13sp"
                android:textColor="#993c3c43"
                android:ellipsize="end"
                android:gravity="center"
                android:id="@+id/tianmu_library_tv_ad_version"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:maxLines="1"
                android:layout_below="@+id/tianmu_library_tv_ad_desc"/>
            <TextView
                android:textSize="13sp"
                android:textColor="#993c3c43"
                android:ellipsize="end"
                android:gravity="center"
                android:id="@+id/tianmu_library_tv_ad_app_developer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:maxLines="1"
                android:layout_below="@+id/tianmu_library_tv_ad_version"/>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="30dp"
                android:layout_below="@+id/tianmu_library_tv_ad_app_developer">
                <TextView
                    android:textSize="13sp"
                    android:textColor="#ff000000"
                    android:id="@+id/tianmu_library_tv_ad_app_permissions"
                    android:padding="4dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="23dp"
                    android:layout_centerInParent="true"
                    android:layout_centerVertical="true"/>
            </RelativeLayout>
        </RelativeLayout>
    </ScrollView>
    <WebView
        android:id="@+id/tianmu_library_webview_info"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/tianmu_library_tv_ad_btn_container"/>
    <ProgressBar
        android:id="@+id/tianmu_library_webview_progress"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:max="100"
        android:indeterminateOnly="false"
        android:layout_alignTop="@+id/tianmu_library_webview_info"
        style="?android:attr/progressBarStyleHorizontal"/>
    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/tianmu_library_tv_ad_btn_container"
        android:background="#f8f8f8"
        android:layout_width="match_parent"
        android:layout_height="88dp"
        android:layout_alignParentBottom="true">
        <LinearLayout
            android:id="@+id/tianmu_library_ll_ad_webview_container"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="88dp">
            <TextView
                android:textSize="16sp"
                android:textColor="#666666"
                android:gravity="center"
                android:id="@+id/tianmu_library_tv_ad_webview_back"
                android:background="@drawable/tianmu_shape_downloading_back_radius8"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginLeft="22dp"
                android:layout_marginTop="17dp"
                android:text="上一步"/>
        </LinearLayout>
        <RelativeLayout
            android:background="#f8f8f8"
            android:layout_width="0dp"
            android:layout_height="88dp"
            android:layout_marginLeft="22dp"
            android:layout_marginRight="22dp"
            android:layout_weight="1"
            android:layout_alignParentBottom="true">
            <TextView
                android:textSize="16sp"
                android:textColor="#ffffff"
                android:gravity="center"
                android:id="@+id/tianmu_library_tv_ad_status"
                android:background="@drawable/tianmu_shape_download_pause_radius8"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="17dp"
                android:text="@string/tianmu_custom_ad_download_now"/>
            <RelativeLayout
                android:gravity="center_horizontal"
                android:id="@+id/tianmu_library_rl_download_pause_layout"
                android:background="@drawable/tianmu_shape_download_pause_radius8"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="17dp">
                <ImageView
                    android:id="@+id/tianmu_library_iv_play"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/tianmu_download_ic_play_24dp"
                    android:layout_centerVertical="true"/>
                <TextView
                    android:textSize="16sp"
                    android:textColor="#ffffff"
                    android:gravity="center"
                    android:id="@+id/tianmu_library_tv_ad_pause_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="6dp"
                    android:layout_toRightOf="@+id/tianmu_library_iv_play"
                    android:layout_centerVertical="true"/>
            </RelativeLayout>
            <RelativeLayout
                android:gravity="center_horizontal"
                android:id="@+id/tianmu_library_rl_downloading_layout"
                android:background="@drawable/tianmu_shape_downloading_radius8"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="17dp">
                <ImageView
                    android:id="@+id/tianmu_library_iv_pause"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/tianmu_download_ic_pause_24dp"
                    android:layout_centerVertical="true"/>
                <TextView
                    android:textSize="16sp"
                    android:textColor="#ff7171"
                    android:gravity="center"
                    android:id="@+id/tianmu_library_tv_ad_downloading_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="6dp"
                    android:layout_toRightOf="@+id/tianmu_library_iv_pause"
                    android:layout_centerVertical="true"/>
            </RelativeLayout>
        </RelativeLayout>
    </LinearLayout>
    <ProgressBar
        android:id="@+id/tianmu_library_progress_bar"
        android:background="@drawable/tianmu_shape_download_dialog_bgd_bar"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:progressDrawable="@drawable/tianmu_shape_download_dialog_progressbar"
        android:layout_alignTop="@+id/tianmu_library_tv_ad_btn_container"
        style="?android:attr/progressBarStyleHorizontal"/>
</RelativeLayout>
