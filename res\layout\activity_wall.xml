<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <RelativeLayout
                android:id="@+id/rl_top"
                android:layout_width="match_parent"
                android:layout_height="40dp">
                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:background="@color/white"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"/>
                <TextView
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/title_color"
                    android:ellipsize="end"
                    android:id="@+id/tv_title"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Title"
                    android:lines="1"
                    android:layout_centerInParent="true"/>
            </RelativeLayout>
            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/container"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"/>
        </LinearLayout>
        <ProgressBar
            android:id="@+id/wall_progress_bar"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="3dp"
            android:max="100"
            android:progress="0"
            android:progressDrawable="@drawable/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"/>
    </RelativeLayout>
</LinearLayout>
