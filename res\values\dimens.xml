<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="abc_action_bar_content_inset_material">16dp</dimen>
    <dimen name="abc_action_bar_content_inset_with_nav">72dp</dimen>
    <dimen name="abc_action_bar_default_height_material">56dp</dimen>
    <dimen name="abc_action_bar_default_padding_end_material">0dp</dimen>
    <dimen name="abc_action_bar_default_padding_start_material">0dp</dimen>
    <dimen name="abc_action_bar_elevation_material">4dp</dimen>
    <dimen name="abc_action_bar_icon_vertical_padding_material">16dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_end_material">10dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_start_material">6dp</dimen>
    <dimen name="abc_action_bar_stacked_max_height">48dp</dimen>
    <dimen name="abc_action_bar_stacked_tab_max_width">180dp</dimen>
    <dimen name="abc_action_bar_subtitle_bottom_margin_material">5dp</dimen>
    <dimen name="abc_action_bar_subtitle_top_margin_material">-3dp</dimen>
    <dimen name="abc_action_button_min_height_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_overflow_material">36dp</dimen>
    <dimen name="abc_alert_dialog_button_bar_height">48dp</dimen>
    <dimen name="abc_alert_dialog_button_dimen">48dp</dimen>
    <dimen name="abc_button_inset_horizontal_material">@dimen/abc_control_inset_material</dimen>
    <dimen name="abc_button_inset_vertical_material">6dp</dimen>
    <dimen name="abc_button_padding_horizontal_material">8dp</dimen>
    <dimen name="abc_button_padding_vertical_material">@dimen/abc_control_padding_material</dimen>
    <dimen name="abc_cascading_menus_min_smallest_width">720dp</dimen>
    <dimen name="abc_config_prefDialogWidth">320dp</dimen>
    <dimen name="abc_control_corner_material">2dp</dimen>
    <dimen name="abc_control_inset_material">4dp</dimen>
    <dimen name="abc_control_padding_material">4dp</dimen>
    <dimen name="abc_dialog_corner_radius_material">2dp</dimen>
    <dimen name="abc_dialog_fixed_height_major">80%</dimen>
    <dimen name="abc_dialog_fixed_height_minor">100%</dimen>
    <dimen name="abc_dialog_fixed_width_major">320dp</dimen>
    <dimen name="abc_dialog_fixed_width_minor">320dp</dimen>
    <dimen name="abc_dialog_list_padding_bottom_no_buttons">8dp</dimen>
    <dimen name="abc_dialog_list_padding_top_no_title">8dp</dimen>
    <dimen name="abc_dialog_min_width_major">65%</dimen>
    <dimen name="abc_dialog_min_width_minor">95%</dimen>
    <dimen name="abc_dialog_padding_material">24dp</dimen>
    <dimen name="abc_dialog_padding_top_material">18dp</dimen>
    <dimen name="abc_dialog_title_divider_material">8dp</dimen>
    <dimen name="abc_disabled_alpha_material_dark">0.3</dimen>
    <dimen name="abc_disabled_alpha_material_light">0.26</dimen>
    <dimen name="abc_dropdownitem_icon_width">32dp</dimen>
    <dimen name="abc_dropdownitem_text_padding_left">8dp</dimen>
    <dimen name="abc_dropdownitem_text_padding_right">8dp</dimen>
    <dimen name="abc_edit_text_inset_bottom_material">7dp</dimen>
    <dimen name="abc_edit_text_inset_horizontal_material">4dp</dimen>
    <dimen name="abc_edit_text_inset_top_material">10dp</dimen>
    <dimen name="abc_floating_window_z">16dp</dimen>
    <dimen name="abc_list_item_height_large_material">80dp</dimen>
    <dimen name="abc_list_item_height_material">64dp</dimen>
    <dimen name="abc_list_item_height_small_material">48dp</dimen>
    <dimen name="abc_list_item_padding_horizontal_material">@dimen/abc_action_bar_content_inset_material</dimen>
    <dimen name="abc_panel_menu_list_width">296dp</dimen>
    <dimen name="abc_progress_bar_height_material">4dp</dimen>
    <dimen name="abc_search_view_preferred_height">48dp</dimen>
    <dimen name="abc_search_view_preferred_width">320dp</dimen>
    <dimen name="abc_seekbar_track_background_height_material">2dp</dimen>
    <dimen name="abc_seekbar_track_progress_height_material">2dp</dimen>
    <dimen name="abc_select_dialog_padding_start_material">20dp</dimen>
    <dimen name="abc_star_big">48dp</dimen>
    <dimen name="abc_star_medium">36dp</dimen>
    <dimen name="abc_star_small">16dp</dimen>
    <dimen name="abc_switch_padding">0px</dimen>
    <dimen name="abc_text_size_body_1_material">14sp</dimen>
    <dimen name="abc_text_size_body_2_material">14sp</dimen>
    <dimen name="abc_text_size_button_material">14sp</dimen>
    <dimen name="abc_text_size_caption_material">12sp</dimen>
    <dimen name="abc_text_size_display_1_material">34sp</dimen>
    <dimen name="abc_text_size_display_2_material">45sp</dimen>
    <dimen name="abc_text_size_display_3_material">56sp</dimen>
    <dimen name="abc_text_size_display_4_material">112sp</dimen>
    <dimen name="abc_text_size_headline_material">24sp</dimen>
    <dimen name="abc_text_size_large_material">22sp</dimen>
    <dimen name="abc_text_size_medium_material">18sp</dimen>
    <dimen name="abc_text_size_menu_header_material">14sp</dimen>
    <dimen name="abc_text_size_menu_material">16sp</dimen>
    <dimen name="abc_text_size_small_material">14sp</dimen>
    <dimen name="abc_text_size_subhead_material">16sp</dimen>
    <dimen name="abc_text_size_subtitle_material_toolbar">16dp</dimen>
    <dimen name="abc_text_size_title_material">20sp</dimen>
    <dimen name="abc_text_size_title_material_toolbar">20dp</dimen>
    <dimen name="action_bar_size">16dp</dimen>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="appcompat_dialog_background_inset">16dp</dimen>
    <dimen name="cardview_compat_inset_shadow">1dp</dimen>
    <dimen name="cardview_default_elevation">2dp</dimen>
    <dimen name="cardview_default_radius">2dp</dimen>
    <dimen name="compat_button_inset_horizontal_material">4dp</dimen>
    <dimen name="compat_button_inset_vertical_material">6dp</dimen>
    <dimen name="compat_button_padding_horizontal_material">8dp</dimen>
    <dimen name="compat_button_padding_vertical_material">4dp</dimen>
    <dimen name="compat_control_corner_material">2dp</dimen>
    <dimen name="compat_notification_large_icon_max_height">320dp</dimen>
    <dimen name="compat_notification_large_icon_max_width">320dp</dimen>
    <dimen name="default_dimension">100dp</dimen>
    <dimen name="design_appbar_elevation">4dp</dimen>
    <dimen name="design_bottom_navigation_active_item_max_width">168dp</dimen>
    <dimen name="design_bottom_navigation_active_item_min_width">96dp</dimen>
    <dimen name="design_bottom_navigation_active_text_size">14sp</dimen>
    <dimen name="design_bottom_navigation_elevation">8dp</dimen>
    <dimen name="design_bottom_navigation_height">56dp</dimen>
    <dimen name="design_bottom_navigation_icon_size">24dp</dimen>
    <dimen name="design_bottom_navigation_item_max_width">96dp</dimen>
    <dimen name="design_bottom_navigation_item_min_width">56dp</dimen>
    <dimen name="design_bottom_navigation_margin">8dp</dimen>
    <dimen name="design_bottom_navigation_shadow_height">1dp</dimen>
    <dimen name="design_bottom_navigation_text_size">12sp</dimen>
    <dimen name="design_bottom_sheet_elevation">8dp</dimen>
    <dimen name="design_bottom_sheet_modal_elevation">16dp</dimen>
    <dimen name="design_bottom_sheet_peek_height_min">64dp</dimen>
    <dimen name="design_fab_border_width">0.5dp</dimen>
    <dimen name="design_fab_elevation">6dp</dimen>
    <dimen name="design_fab_image_size">24dp</dimen>
    <dimen name="design_fab_size_mini">40dp</dimen>
    <dimen name="design_fab_size_normal">56dp</dimen>
    <dimen name="design_fab_translation_z_hovered_focused">6dp</dimen>
    <dimen name="design_fab_translation_z_pressed">6dp</dimen>
    <dimen name="design_navigation_elevation">16dp</dimen>
    <dimen name="design_navigation_icon_padding">32dp</dimen>
    <dimen name="design_navigation_icon_size">24dp</dimen>
    <dimen name="design_navigation_item_horizontal_padding">16dp</dimen>
    <dimen name="design_navigation_item_icon_padding">32dp</dimen>
    <dimen name="design_navigation_max_width">280dp</dimen>
    <dimen name="design_navigation_padding_bottom">8dp</dimen>
    <dimen name="design_navigation_separator_vertical_padding">8dp</dimen>
    <dimen name="design_snackbar_action_inline_max_width">128dp</dimen>
    <dimen name="design_snackbar_action_text_color_alpha">1</dimen>
    <dimen name="design_snackbar_background_corner_radius">0dp</dimen>
    <dimen name="design_snackbar_elevation">6dp</dimen>
    <dimen name="design_snackbar_extra_spacing_horizontal">0dp</dimen>
    <dimen name="design_snackbar_max_width">-1px</dimen>
    <dimen name="design_snackbar_min_width">-1px</dimen>
    <dimen name="design_snackbar_padding_horizontal">12dp</dimen>
    <dimen name="design_snackbar_padding_vertical">14dp</dimen>
    <dimen name="design_snackbar_padding_vertical_2lines">24dp</dimen>
    <dimen name="design_snackbar_text_size">14sp</dimen>
    <dimen name="design_tab_max_width">264dp</dimen>
    <dimen name="design_tab_scrollable_min_width">72dp</dimen>
    <dimen name="design_tab_text_size">14sp</dimen>
    <dimen name="design_tab_text_size_2line">12sp</dimen>
    <dimen name="design_textinput_caption_translate_y">5dp</dimen>
    <dimen name="disabled_alpha_material_dark">0.3</dimen>
    <dimen name="disabled_alpha_material_light">0.26</dimen>
    <dimen name="fastscroll_default_thickness">8dp</dimen>
    <dimen name="fastscroll_margin">0dp</dimen>
    <dimen name="fastscroll_minimum_range">50dp</dimen>
    <dimen name="highlight_alpha_material_colored">0.26</dimen>
    <dimen name="highlight_alpha_material_dark">0.2</dimen>
    <dimen name="highlight_alpha_material_light">0.12</dimen>
    <dimen name="hint_alpha_material_dark">0.5</dimen>
    <dimen name="hint_alpha_material_light">0.38</dimen>
    <dimen name="hint_pressed_alpha_material_dark">0.7</dimen>
    <dimen name="hint_pressed_alpha_material_light">0.54</dimen>
    <dimen name="item_touch_helper_max_drag_scroll_per_frame">20dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_max_velocity">800dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_velocity">120dp</dimen>
    <dimen name="material_emphasis_disabled">0.38</dimen>
    <dimen name="material_emphasis_high_type">0.87</dimen>
    <dimen name="material_emphasis_medium">0.6</dimen>
    <dimen name="material_text_view_test_line_height">200px</dimen>
    <dimen name="material_text_view_test_line_height_override">100px</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_bottom">80dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_end">24dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_start">24dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_top">80dp</dimen>
    <dimen name="mtrl_alert_dialog_picker_background_inset">24dp</dimen>
    <dimen name="mtrl_badge_horizontal_edge_offset">4dp</dimen>
    <dimen name="mtrl_badge_long_text_horizontal_padding">4dp</dimen>
    <dimen name="mtrl_badge_radius">4dp</dimen>
    <dimen name="mtrl_badge_text_horizontal_edge_offset">6dp</dimen>
    <dimen name="mtrl_badge_text_size">10sp</dimen>
    <dimen name="mtrl_badge_with_text_radius">8dp</dimen>
    <dimen name="mtrl_bottomappbar_fabOffsetEndMode">60dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_bottom_margin">16dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_margin">5dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius">8dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_vertical_offset">0dp</dimen>
    <dimen name="mtrl_bottomappbar_height">56dp</dimen>
    <dimen name="mtrl_btn_corner_radius">4dp</dimen>
    <dimen name="mtrl_btn_dialog_btn_min_width">64dp</dimen>
    <dimen name="mtrl_btn_disabled_elevation">0dp</dimen>
    <dimen name="mtrl_btn_disabled_z">0dp</dimen>
    <dimen name="mtrl_btn_elevation">2dp</dimen>
    <dimen name="mtrl_btn_focused_z">2dp</dimen>
    <dimen name="mtrl_btn_hovered_z">2dp</dimen>
    <dimen name="mtrl_btn_icon_btn_padding_left">12dp</dimen>
    <dimen name="mtrl_btn_icon_padding">8dp</dimen>
    <dimen name="mtrl_btn_inset">6dp</dimen>
    <dimen name="mtrl_btn_letter_spacing">0.07</dimen>
    <dimen name="mtrl_btn_padding_bottom">4dp</dimen>
    <dimen name="mtrl_btn_padding_left">16dp</dimen>
    <dimen name="mtrl_btn_padding_right">16dp</dimen>
    <dimen name="mtrl_btn_padding_top">4dp</dimen>
    <dimen name="mtrl_btn_pressed_z">6dp</dimen>
    <dimen name="mtrl_btn_stroke_size">1dp</dimen>
    <dimen name="mtrl_btn_text_btn_icon_padding">4dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_left">8dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_right">8dp</dimen>
    <dimen name="mtrl_btn_text_size">14sp</dimen>
    <dimen name="mtrl_btn_z">0dp</dimen>
    <dimen name="mtrl_calendar_action_height">52dp</dimen>
    <dimen name="mtrl_calendar_action_padding">8dp</dimen>
    <dimen name="mtrl_calendar_bottom_padding">0dp</dimen>
    <dimen name="mtrl_calendar_content_padding">12dp</dimen>
    <dimen name="mtrl_calendar_day_corner">15dp</dimen>
    <dimen name="mtrl_calendar_day_height">32dp</dimen>
    <dimen name="mtrl_calendar_day_horizontal_padding">3dp</dimen>
    <dimen name="mtrl_calendar_day_today_stroke">1dp</dimen>
    <dimen name="mtrl_calendar_day_vertical_padding">1dp</dimen>
    <dimen name="mtrl_calendar_day_width">36dp</dimen>
    <dimen name="mtrl_calendar_days_of_week_height">24dp</dimen>
    <dimen name="mtrl_calendar_dialog_background_inset">16dp</dimen>
    <dimen name="mtrl_calendar_header_content_padding">12dp</dimen>
    <dimen name="mtrl_calendar_header_content_padding_fullscreen">4dp</dimen>
    <dimen name="mtrl_calendar_header_divider_thickness">1dp</dimen>
    <dimen name="mtrl_calendar_header_height">120dp</dimen>
    <dimen name="mtrl_calendar_header_height_fullscreen">128dp</dimen>
    <dimen name="mtrl_calendar_header_selection_line_height">32dp</dimen>
    <dimen name="mtrl_calendar_header_text_padding">12dp</dimen>
    <dimen name="mtrl_calendar_header_toggle_margin_bottom">8dp</dimen>
    <dimen name="mtrl_calendar_header_toggle_margin_top">24dp</dimen>
    <dimen name="mtrl_calendar_landscape_header_width">0dp</dimen>
    <dimen name="mtrl_calendar_maximum_default_fullscreen_minor_axis">480dp</dimen>
    <dimen name="mtrl_calendar_month_horizontal_padding">2dp</dimen>
    <dimen name="mtrl_calendar_month_vertical_padding">0dp</dimen>
    <dimen name="mtrl_calendar_navigation_bottom_padding">4dp</dimen>
    <dimen name="mtrl_calendar_navigation_height">48dp</dimen>
    <dimen name="mtrl_calendar_navigation_top_padding">4dp</dimen>
    <dimen name="mtrl_calendar_pre_l_text_clip_padding">0dp</dimen>
    <dimen name="mtrl_calendar_selection_baseline_to_top_fullscreen">104dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_bottom">20dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_bottom_fullscreen">24dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_top">100dp</dimen>
    <dimen name="mtrl_calendar_text_input_padding_top">16dp</dimen>
    <dimen name="mtrl_calendar_title_baseline_to_top">28dp</dimen>
    <dimen name="mtrl_calendar_title_baseline_to_top_fullscreen">68dp</dimen>
    <dimen name="mtrl_calendar_year_corner">18dp</dimen>
    <dimen name="mtrl_calendar_year_height">52dp</dimen>
    <dimen name="mtrl_calendar_year_horizontal_padding">8dp</dimen>
    <dimen name="mtrl_calendar_year_vertical_padding">8dp</dimen>
    <dimen name="mtrl_calendar_year_width">88dp</dimen>
    <dimen name="mtrl_card_checked_icon_margin">8dp</dimen>
    <dimen name="mtrl_card_checked_icon_size">24dp</dimen>
    <dimen name="mtrl_card_corner_radius">4dp</dimen>
    <dimen name="mtrl_card_dragged_z">5dp</dimen>
    <dimen name="mtrl_card_elevation">1dp</dimen>
    <dimen name="mtrl_card_spacing">8dp</dimen>
    <dimen name="mtrl_chip_pressed_translation_z">3dp</dimen>
    <dimen name="mtrl_chip_text_size">14sp</dimen>
    <dimen name="mtrl_edittext_rectangle_top_offset">12dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_elevation">8dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_vertical_offset">1dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_vertical_padding">8dp</dimen>
    <dimen name="mtrl_extended_fab_bottom_padding">12dp</dimen>
    <dimen name="mtrl_extended_fab_corner_radius">24dp</dimen>
    <dimen name="mtrl_extended_fab_disabled_elevation">0dp</dimen>
    <dimen name="mtrl_extended_fab_disabled_translation_z">0dp</dimen>
    <dimen name="mtrl_extended_fab_elevation">6dp</dimen>
    <dimen name="mtrl_extended_fab_end_padding">20dp</dimen>
    <dimen name="mtrl_extended_fab_end_padding_icon">24dp</dimen>
    <dimen name="mtrl_extended_fab_icon_size">24dp</dimen>
    <dimen name="mtrl_extended_fab_icon_text_spacing">16dp</dimen>
    <dimen name="mtrl_extended_fab_min_height">48dp</dimen>
    <dimen name="mtrl_extended_fab_min_width">120dp</dimen>
    <dimen name="mtrl_extended_fab_start_padding">20dp</dimen>
    <dimen name="mtrl_extended_fab_start_padding_icon">16dp</dimen>
    <dimen name="mtrl_extended_fab_top_padding">12dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_base">0dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_pressed">6dp</dimen>
    <dimen name="mtrl_fab_elevation">6dp</dimen>
    <dimen name="mtrl_fab_min_touch_target">48dp</dimen>
    <dimen name="mtrl_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="mtrl_fab_translation_z_pressed">6dp</dimen>
    <dimen name="mtrl_high_ripple_default_alpha">0.48</dimen>
    <dimen name="mtrl_high_ripple_focused_alpha">0.48</dimen>
    <dimen name="mtrl_high_ripple_hovered_alpha">0.16</dimen>
    <dimen name="mtrl_high_ripple_pressed_alpha">0.48</dimen>
    <dimen name="mtrl_large_touch_target">100dp</dimen>
    <dimen name="mtrl_low_ripple_default_alpha">0.24</dimen>
    <dimen name="mtrl_low_ripple_focused_alpha">0.24</dimen>
    <dimen name="mtrl_low_ripple_hovered_alpha">0.08</dimen>
    <dimen name="mtrl_low_ripple_pressed_alpha">0.24</dimen>
    <dimen name="mtrl_min_touch_target_size">48dp</dimen>
    <dimen name="mtrl_navigation_elevation">0dp</dimen>
    <dimen name="mtrl_navigation_item_horizontal_padding">22dp</dimen>
    <dimen name="mtrl_navigation_item_icon_padding">14dp</dimen>
    <dimen name="mtrl_navigation_item_icon_size">24dp</dimen>
    <dimen name="mtrl_navigation_item_shape_horizontal_margin">8dp</dimen>
    <dimen name="mtrl_navigation_item_shape_vertical_margin">4dp</dimen>
    <dimen name="mtrl_shape_corner_size_large_component">0dp</dimen>
    <dimen name="mtrl_shape_corner_size_medium_component">4dp</dimen>
    <dimen name="mtrl_shape_corner_size_small_component">4dp</dimen>
    <dimen name="mtrl_slider_halo_radius">24dp</dimen>
    <dimen name="mtrl_slider_label_padding">4dp</dimen>
    <dimen name="mtrl_slider_label_radius">13dp</dimen>
    <dimen name="mtrl_slider_label_square_side">26dp</dimen>
    <dimen name="mtrl_slider_thumb_elevation">1dp</dimen>
    <dimen name="mtrl_slider_thumb_radius">10dp</dimen>
    <dimen name="mtrl_slider_track_height">4dp</dimen>
    <dimen name="mtrl_slider_track_side_padding">16dp</dimen>
    <dimen name="mtrl_slider_track_top">24dp</dimen>
    <dimen name="mtrl_slider_widget_height">48dp</dimen>
    <dimen name="mtrl_snackbar_action_text_color_alpha">0.5</dimen>
    <dimen name="mtrl_snackbar_background_corner_radius">4dp</dimen>
    <dimen name="mtrl_snackbar_background_overlay_color_alpha">0.8</dimen>
    <dimen name="mtrl_snackbar_margin">8dp</dimen>
    <dimen name="mtrl_switch_thumb_elevation">4dp</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_medium">4dp</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_small">0dp</dimen>
    <dimen name="mtrl_textinput_box_label_cutout_padding">4dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_default">1dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_focused">2dp</dimen>
    <dimen name="mtrl_textinput_counter_margin_start">16dp</dimen>
    <dimen name="mtrl_textinput_end_icon_margin_start">4dp</dimen>
    <dimen name="mtrl_textinput_outline_box_expanded_padding">16dp</dimen>
    <dimen name="mtrl_textinput_start_icon_margin_end">4dp</dimen>
    <dimen name="mtrl_toolbar_default_height">56dp</dimen>
    <dimen name="mtrl_tooltip_arrowSize">14dp</dimen>
    <dimen name="mtrl_tooltip_cornerSize">4dp</dimen>
    <dimen name="mtrl_tooltip_minHeight">32dp</dimen>
    <dimen name="mtrl_tooltip_minWidth">32dp</dimen>
    <dimen name="mtrl_tooltip_padding">12dp</dimen>
    <dimen name="mtrl_transition_shared_axis_slide_distance">30dp</dimen>
    <dimen name="notification_action_icon_size">32dp</dimen>
    <dimen name="notification_action_text_size">13sp</dimen>
    <dimen name="notification_big_circle_margin">12dp</dimen>
    <dimen name="notification_content_margin_start">0dp</dimen>
    <dimen name="notification_large_icon_height">64dp</dimen>
    <dimen name="notification_large_icon_width">64dp</dimen>
    <dimen name="notification_main_column_padding_top">0dp</dimen>
    <dimen name="notification_media_narrow_margin">12dp</dimen>
    <dimen name="notification_right_icon_size">16dp</dimen>
    <dimen name="notification_right_side_padding_top">4dp</dimen>
    <dimen name="notification_small_icon_background_padding">3dp</dimen>
    <dimen name="notification_small_icon_size_as_large">24dp</dimen>
    <dimen name="notification_subtext_size">13sp</dimen>
    <dimen name="notification_top_pad">10dp</dimen>
    <dimen name="notification_top_pad_large_text">5dp</dimen>
    <dimen name="subtitle_corner_radius">2dp</dimen>
    <dimen name="subtitle_outline_width">2dp</dimen>
    <dimen name="subtitle_shadow_offset">2dp</dimen>
    <dimen name="subtitle_shadow_radius">2dp</dimen>
    <dimen name="test_mtrl_calendar_day_cornerSize">52dp</dimen>
    <dimen name="tianmu_video_controller_height">46dp</dimen>
    <dimen name="tianmu_video_controller_icon_padding">12dp</dimen>
    <dimen name="tianmu_video_controller_seekbar_max_size">1dp</dimen>
    <dimen name="tianmu_video_controller_seekbar_size_n">14dp</dimen>
    <dimen name="tianmu_video_controller_seekbar_size_s">14dp</dimen>
    <dimen name="tianmu_video_controller_text_size">16sp</dimen>
    <dimen name="tianmu_video_controller_time_text_size">14sp</dimen>
    <dimen name="tianmu_video_default_spacing">10dp</dimen>
    <dimen name="tianmu_video_play_btn_size">50dp</dimen>
    <dimen name="tooltip_corner_radius">2dp</dimen>
    <dimen name="tooltip_horizontal_padding">16dp</dimen>
    <dimen name="tooltip_margin">8dp</dimen>
    <dimen name="tooltip_precise_anchor_extra_offset">8dp</dimen>
    <dimen name="tooltip_precise_anchor_threshold">96dp</dimen>
    <dimen name="tooltip_vertical_padding">6.5dp</dimen>
    <dimen name="tooltip_y_offset_non_touch">0dp</dimen>
    <dimen name="tooltip_y_offset_touch">16dp</dimen>
</resources>
