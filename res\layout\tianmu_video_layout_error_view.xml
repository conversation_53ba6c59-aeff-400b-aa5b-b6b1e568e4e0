<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@android:color/black"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textColor="@android:color/white"
            android:gravity="center"
            android:id="@+id/tianmu_video_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tianmu_video_error_message"/>
        <TextView
            android:textColor="@android:color/white"
            android:gravity="center"
            android:id="@+id/tianmu_video_status_btn"
            android:background="@drawable/tianmu_video_shape_status_view_btn"
            android:paddingLeft="18dp"
            android:paddingTop="4dp"
            android:paddingRight="18dp"
            android:paddingBottom="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/tianmu_video_retry"/>
    </LinearLayout>
</FrameLayout>
