<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="#ffffff"
    android:layout_width="match_parent"
    android:layout_height="100dp">
    <ImageView
        android:id="@+id/tianmu_banner_iv_pic"
        android:background="#ececec"
        android:layout_width="176dp"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:layout_alignParentLeft="true"/>
    <TextView
        android:id="@+id/tianmu_banner_tv_ad_target"
        android:layout_alignRight="@+id/tianmu_banner_iv_pic"
        android:layout_alignBottom="@+id/tianmu_banner_iv_pic"
        style="@style/tianmu_ad_target_base_style"/>
    <TextView
        android:id="@+id/tianmu_banner_tv_ad_source"
        android:layout_alignLeft="@+id/tianmu_banner_iv_pic"
        android:layout_alignTop="@+id/tianmu_banner_iv_pic"
        style="@style/tianmu_ad_source_base_style"/>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/tianmu_banner_content_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="28dp"
        android:layout_toRightOf="@+id/tianmu_banner_iv_pic">
        <TextView
            android:textSize="16sp"
            android:textColor="#000000"
            android:ellipsize="end"
            android:id="@+id/tianmu_banner_tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/tianmu_custom_ad_title"
            android:lines="1"
            android:singleLine="true"/>
        <TextView
            android:textSize="14sp"
            android:textColor="#6d7278"
            android:ellipsize="end"
            android:id="@+id/tianmu_banner_tv_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/tianmu_custom_ad_content"
            android:lines="1"
            android:lineSpacingExtra="2dp"/>
    </LinearLayout>
    <TextView
        android:textSize="14sp"
        android:textColor="#ffffff"
        android:ellipsize="end"
        android:gravity="center"
        android:id="@+id/tianmu_banner_tv_action_button"
        android:background="@drawable/tianmu_bg_banner_action_button"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="8dp"
        android:minWidth="80dp"
        android:text="@string/tianmu_custom_ad_download_now"
        android:lines="1"
        android:singleLine="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"/>
    <ImageView
        android:id="@+id/tianmu_banner_iv_close"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="8dp"
        android:src="@drawable/tianmu_icon_round_close"
        android:scaleType="centerCrop"
        android:layout_alignParentRight="true"/>
</RelativeLayout>
