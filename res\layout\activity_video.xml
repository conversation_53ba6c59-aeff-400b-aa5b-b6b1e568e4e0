<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tbLayout"
        android:background="@color/black"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tabGravity="center"
        app:tabIndicatorColor="@color/white"
        app:tabIndicatorFullWidth="false"
        app:tabSelectedTextColor="@color/white"
        app:tabTextColor="#999999"/>
    <FrameLayout
        android:id="@+id/fl_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</LinearLayout>
