<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:id="@+id/content_alliance_ad_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:src="@drawable/btn_back"/>
    <androidx.appcompat.widget.LinearLayoutCompat
        android:gravity="center_horizontal"
        android:layout_gravity="right"
        android:orientation="vertical"
        android:id="@+id/ll_reward2"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="60dp"
        android:layout_marginEnd="10dp">
        <ImageView
            android:layout_width="120dp"
            android:layout_height="90dp"
            android:src="@mipmap/cd"
            android:scaleType="centerCrop"/>
    </androidx.appcompat.widget.LinearLayoutCompat>
    <RelativeLayout
        android:layout_gravity="right"
        android:id="@+id/alreadyGetMoneyBox"
        android:background="#fff"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="100px"
        android:layout_marginTop="30dp"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp">
        <androidx.appcompat.widget.LinearLayoutCompat
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true">
            <TextView
                android:textSize="40px"
                android:textColor="#000"
                android:gravity="center"
                android:id="@+id/alreadyGetMoney"
                android:layout_width="match_parent"
                android:layout_height="100px"
                android:text="已获得奖励"/>
        </androidx.appcompat.widget.LinearLayoutCompat>
    </RelativeLayout>
    <RelativeLayout
        android:layout_gravity="right"
        android:id="@+id/rl_pay"
        android:visibility="invisible"
        android:layout_width="210px"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:layout_marginEnd="5dp">
        <androidx.appcompat.widget.LinearLayoutCompat
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:id="@+id/ll_tip"
            android:background="@mipmap/fenhong"
            android:visibility="invisible"
            android:layout_width="match_parent"
            android:layout_height="260px"
            android:layout_marginTop="8dp"
            android:layout_alignParentEnd="true">
            <TextView
                android:textSize="30px"
                android:textColor="#ff3157"
                android:gravity="center"
                android:layout_width="match_parent"
                android:layout_height="70px"
                android:layout_marginLeft="2dp"
                android:layout_marginTop="10px"
                android:layout_marginRight="2dp"
                android:text="已获得奖励"
                android:layout_marginHorizontal="2dp"/>
            <TextView
                android:textSize="50px"
                android:textColor="#ff3157"
                android:gravity="center"
                android:id="@+id/tv_tip"
                android:layout_width="match_parent"
                android:layout_height="80px"
                android:layout_marginLeft="2dp"
                android:layout_marginRight="2dp"
                android:text="0.00"
                android:layout_marginHorizontal="2dp"/>
            <TextView
                android:textSize="32px"
                android:textColor="#fff"
                android:gravity="center"
                android:layout_width="match_parent"
                android:layout_height="40px"
                android:layout_marginLeft="2dp"
                android:layout_marginTop="30px"
                android:layout_marginRight="2dp"
                android:text="大拇指视频"
                android:layout_marginHorizontal="2dp"/>
        </androidx.appcompat.widget.LinearLayoutCompat>
    </RelativeLayout>
</FrameLayout>
