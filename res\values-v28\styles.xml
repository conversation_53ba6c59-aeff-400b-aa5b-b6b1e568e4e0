<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.Theme.AppCompat" parent="@style/Base.V28.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V28.Theme.AppCompat.Light">
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="@style/ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:backgroundDimAmount">0.32</item>
        <item name="android:checkedTextViewStyle">@style/Widget.MaterialComponents.CheckedTextView</item>
        <item name="android:dialogCornerRadius">@null</item>
        <item name="alertDialogStyle">@style/MaterialAlertDialog.MaterialComponents</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNeutralButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.MaterialComponents.Body.Text</item>
    </style>
    <style name="Base.V28.Theme.AppCompat" parent="@style/Base.V26.Theme.AppCompat">
        <item name="dialogCornerRadius">?android:attr/dialogCornerRadius</item>
    </style>
    <style name="Base.V28.Theme.AppCompat.Light" parent="@style/Base.V26.Theme.AppCompat.Light">
        <item name="dialogCornerRadius">?android:attr/dialogCornerRadius</item>
    </style>
    <style name="tianmu_video_no_status" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
</resources>
