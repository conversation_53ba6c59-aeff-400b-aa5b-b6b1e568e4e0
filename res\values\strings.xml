<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="androidx_startup">androidx.startup</string>
    <string name="app_load_mask">加载中...若长时间未加载完成，请联系管理员！</string>
    <string name="app_lz_sdk">playmy_jfq</string>
    <string name="app_name">群英会</string>
    <string name="app_update_warn">请尽快更新app</string>
    <string name="appbar_scrolling_view_behavior">com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior</string>
    <string name="basepopup_error_decorview">PopupWindow需要%s的DecorView的WindowToken，请检查是否存在DecorView</string>
    <string name="basepopup_error_destroyed">该BasePopup已经被Destroy，无法继续执行show</string>
    <string name="basepopup_error_non_act_context">找不到宿主Activity，请确保您至少打开了一个Activity</string>
    <string name="basepopup_error_thread">请在主线程操作</string>
    <string name="basepopup_has_been_shown">BasePopup已经显示了</string>
    <string name="basepopup_host">宿主（%s）</string>
    <string name="basepopup_host_destroyed">宿主已经被销毁</string>
    <string name="basepopup_shown_successful">弹窗成功</string>
    <string name="basepopup_window_not_prepare">%s窗口尚未准备好，等待准备就绪后弹出</string>
    <string name="basepopup_window_prepared">%s窗口已经准备好，执行弹出</string>
    <string name="bottom_sheet_behavior">com.google.android.material.bottomsheet.BottomSheetBehavior</string>
    <string name="character_counter_content_description">Characters entered %1$d of %2$d</string>
    <string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern">%1$d/%2$d</string>
    <string name="chip_text">Chip text</string>
    <string name="clear_text_end_icon_content_description">Clear text</string>
    <string name="common_permission_access_media_location">read media file location permission</string>
    <string name="common_permission_activity_recognition_api29">physical activity permission</string>
    <string name="common_permission_activity_recognition_api30">physical activity permission</string>
    <string name="common_permission_alarms_reminders">alarms &amp; reminders permission</string>
    <string name="common_permission_alert">Authorization reminder</string>
    <string name="common_permission_all_file_access">all file access permission</string>
    <string name="common_permission_allow_notifications">allow notifications permission</string>
    <string name="common_permission_allow_notifications_access">allow notifications access permission</string>
    <string name="common_permission_apps_with_usage_access">apps with usage access permission</string>
    <string name="common_permission_background_default_option_label">Allow all the time</string>
    <string name="common_permission_background_location_fail_hint">Failed to obtain background location permission,\nPlease select %s option</string>
    <string name="common_permission_background_sensors_fail_hint">Failed to obtain background sensor permission, please select %s sensor permission</string>
    <string name="common_permission_body_sensors">body sensors permission</string>
    <string name="common_permission_body_sensors_background">background body sensors permission</string>
    <string name="common_permission_calendar">calendar permission</string>
    <string name="common_permission_call_logs">call logs permission</string>
    <string name="common_permission_camera">camera permission</string>
    <string name="common_permission_colon">:</string>
    <string name="common_permission_comma">,</string>
    <string name="common_permission_contacts">contacts permission</string>
    <string name="common_permission_denied">Denied</string>
    <string name="common_permission_description_title">Permission description</string>
    <string name="common_permission_display_over_other_apps">display over other apps permission</string>
    <string name="common_permission_do_not_disturb_access">do not disturb access permission</string>
    <string name="common_permission_fail_assign_hint">Authorization failed, please grant %s correctly</string>
    <string name="common_permission_fail_hint">Authorization failed, please grant permission correctly</string>
    <string name="common_permission_get_installed_apps">get info about installed apps permission</string>
    <string name="common_permission_goto_setting_page">Go to authorization</string>
    <string name="common_permission_granted">Granted</string>
    <string name="common_permission_ignore_battery_optimize">ignore battery optimize permission</string>
    <string name="common_permission_image_and_video">image and video permission</string>
    <string name="common_permission_install_unknown_apps">install unknown apps permission</string>
    <string name="common_permission_location">location permission</string>
    <string name="common_permission_location_background">background location permission</string>
    <string name="common_permission_manual_assign_fail_background_location_hint">Failed to obtain the background location permission, please select %s in the location permission</string>
    <string name="common_permission_manual_assign_fail_background_sensors_hint">Failed to obtain the background sensor permission, please select %s in the sensor permission</string>
    <string name="common_permission_manual_assign_fail_hint">Failed to obtain permission, please grant %s manually</string>
    <string name="common_permission_manual_fail_hint">Failed to obtain permission, please grant permission manually</string>
    <string name="common_permission_media_location_hint_fail">Failed to get media location permission\nPlease clear app data and try again</string>
    <string name="common_permission_microphone">microphone permission</string>
    <string name="common_permission_modify_system_settings">modify system settings permission</string>
    <string name="common_permission_music_and_audio">music and audio permission</string>
    <string name="common_permission_nearby_devices">nearby devices permission</string>
    <string name="common_permission_phone">phone permission</string>
    <string name="common_permission_picture_in_picture">picture-in-picture permission</string>
    <string name="common_permission_post_notifications">send notification permission</string>
    <string name="common_permission_sms">sms permission</string>
    <string name="common_permission_storage">storage permission</string>
    <string name="common_permission_unknown">permission</string>
    <string name="common_permission_vpn">\tVPN\tpermission</string>
    <string name="default_filedownloader_notification_content">FileDownloader is running.</string>
    <string name="default_filedownloader_notification_title">FileDownloader</string>
    <string name="error_icon_content_description">Error</string>
    <string name="exposed_dropdown_menu_content_description">Show dropdown menu</string>
    <string name="fab_transformation_scrim_behavior">com.google.android.material.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior">com.google.android.material.transformation.FabTransformationSheetBehavior</string>
    <string name="hide_bottom_view_on_scroll_behavior">com.google.android.material.behavior.HideBottomViewOnScrollBehavior</string>
    <string name="icon_content_description">Dialog Icon</string>
    <string name="identifier_hiad_str_2">245d64e65dc9fe70d4d62aa6b941221fa92a3fb07db7a4858e43bf1dbf2972e9</string>
    <string name="identifier_hiad_str_3">9b38b1ce5d9b5bba1a6539ad75eae153555c74f5b95e6cdfe5019a6a0e56f466</string>
    <string name="item_view_role_description">Tab</string>
    <string name="material_slider_range_end">Range end,</string>
    <string name="material_slider_range_start">Range start,</string>
    <string name="mtrl_badge_numberless_content_description">New notification</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="mtrl_exceed_max_badge_number_content_description">More than %1$d new notifications</string>
    <string name="mtrl_exceed_max_badge_number_suffix">%1$d%2$s</string>
    <string name="mtrl_picker_a11y_next_month">Change to next month</string>
    <string name="mtrl_picker_a11y_prev_month">Change to previous month</string>
    <string name="mtrl_picker_announce_current_selection">Current selection: %1$s</string>
    <string name="mtrl_picker_cancel">@android:string/cancel</string>
    <string name="mtrl_picker_confirm">@android:string/ok</string>
    <string name="mtrl_picker_date_header_selected">%1$s</string>
    <string name="mtrl_picker_date_header_title">Select a Date</string>
    <string name="mtrl_picker_date_header_unselected">Selected date</string>
    <string name="mtrl_picker_day_of_week_column_header">Column of days: %1$s</string>
    <string name="mtrl_picker_invalid_format">Invalid format.</string>
    <string name="mtrl_picker_invalid_format_example">Example: %1$s</string>
    <string name="mtrl_picker_invalid_format_use">Use: %1$s</string>
    <string name="mtrl_picker_invalid_range">Invalid range.</string>
    <string name="mtrl_picker_navigate_to_year_description">Navigate to year %1$s</string>
    <string name="mtrl_picker_out_of_range">Out of range: %1$s</string>
    <string name="mtrl_picker_range_header_only_end_selected">Start date – %1$s</string>
    <string name="mtrl_picker_range_header_only_start_selected">%1$s – End date</string>
    <string name="mtrl_picker_range_header_selected">%1$s – %2$s</string>
    <string name="mtrl_picker_range_header_title">Select a Date Range</string>
    <string name="mtrl_picker_range_header_unselected">Start date – End date</string>
    <string name="mtrl_picker_save">Save</string>
    <string name="mtrl_picker_text_input_date_hint">Date</string>
    <string name="mtrl_picker_text_input_date_range_end_hint">End date</string>
    <string name="mtrl_picker_text_input_date_range_start_hint">Start date</string>
    <string name="mtrl_picker_text_input_day_abbr">d</string>
    <string name="mtrl_picker_text_input_month_abbr">m</string>
    <string name="mtrl_picker_text_input_year_abbr">y</string>
    <string name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string>
    <string name="mtrl_picker_toggle_to_day_selection">Tap to switch to selecting a day</string>
    <string name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string>
    <string name="mtrl_picker_toggle_to_year_selection">Tap to switch to selecting a year</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string>
    <string name="search_menu_title">Search</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="tianmu_app_detail">应用详情</string>
    <string name="tianmu_app_do_not_remind">不再提示</string>
    <string name="tianmu_app_install_now">立即安装</string>
    <string name="tianmu_app_not_installed">检测到已下载未安装应用</string>
    <string name="tianmu_cancel">取消</string>
    <string name="tianmu_confirm">确认</string>
    <string name="tianmu_custom_ad_ad_target">广告</string>
    <string name="tianmu_custom_ad_application_permissions">应用权限</string>
    <string name="tianmu_custom_ad_check_details">查看详情</string>
    <string name="tianmu_custom_ad_content">内容</string>
    <string name="tianmu_custom_ad_download_give_up">放弃下载&gt;&gt;</string>
    <string name="tianmu_custom_ad_download_now">立即下载</string>
    <string name="tianmu_custom_ad_download_now2">立即下载&gt;&gt;</string>
    <string name="tianmu_custom_ad_download_status_pause">暂停中</string>
    <string name="tianmu_custom_ad_download_status_start">正在下载</string>
    <string name="tianmu_custom_ad_platform_target">天目广告</string>
    <string name="tianmu_custom_ad_privacy_policy">隐私政策</string>
    <string name="tianmu_custom_ad_title">标题</string>
    <string name="tianmu_custom_ad_video_continue_exit">继续退出</string>
    <string name="tianmu_custom_ad_video_keep_watch">继续观看视频</string>
    <string name="tianmu_dialog_notice_apply_message">为了展示下载进度，我们将申请通知权限。请点击允许，同意通知权限的申请。</string>
    <string name="tianmu_dialog_notice_apply_title">允许通知权限</string>
    <string name="tianmu_interaction_jump_tips">跳转到第三方应用或详情页</string>
    <string name="tianmu_interaction_shake_the_phone">点击或摇一摇查看</string>
    <string name="tianmu_interaction_slide_up">点击或上滑查看</string>
    <string name="tianmu_interaction_slide_up2">向上滑动</string>
    <string name="tianmu_interaction_turn_the_phone">点击或转动手机查看</string>
    <string name="tianmu_interaction_turn_the_phone2">扭动手机</string>
    <string name="tianmu_page_exception">页面异常</string>
    <string name="tianmu_page_exception_please_close">页面异常，请关闭后重试</string>
    <string name="tianmu_reward_achieve">恭喜获得奖励</string>
    <string name="tianmu_reward_achieve_count_down">奖励将于%1$秒后发放</string>
    <string name="tianmu_slide_to_learn_more">滑动了解更多</string>
    <string name="tianmu_slide_to_right_check">点击或右滑查看</string>
    <string name="tianmu_slide_to_right_check2">向右滑动</string>
    <string name="tianmu_slide_to_right_check3">全方向滑动</string>
    <string name="tianmu_slide_to_see_details">滑动\n查看详情</string>
    <string name="tianmu_video_continue_play">继续播放</string>
    <string name="tianmu_video_error_message">出了点小问题，请稍后重试</string>
    <string name="tianmu_video_replay">重新播放</string>
    <string name="tianmu_video_retry">重 试</string>
    <string name="tianmu_video_wifi_tip">您正在使用移动网络，继续播放将消耗流量</string>
    <string name="tianmu_wipe_to_see_details">擦一擦查看</string>
    <string name="title_dashboard">激励demo</string>
    <string name="title_home">插屏demo</string>
    <string name="title_ks_video">短视频demo</string>
    <string name="title_notifications">信息流demo</string>
    <string name="versionchecklib_cancel">取消</string>
    <string name="versionchecklib_check_new_version">检测到新版本</string>
    <string name="versionchecklib_confirm">确认</string>
    <string name="versionchecklib_download_apkname">%s.apk</string>
    <string name="versionchecklib_download_fail">下载失败，点击重试</string>
    <string name="versionchecklib_download_fail_retry">下载失败是否重试？</string>
    <string name="versionchecklib_download_finish">下载完成，点击安装</string>
    <string name="versionchecklib_download_progress">下载进度:%d%%/100%%</string>
    <string name="versionchecklib_downloading">正在下载中...</string>
    <string name="versionchecklib_progress">%d/100</string>
    <string name="versionchecklib_retry">重试</string>
    <string name="versionchecklib_version_service_runing">更新服务正在运行</string>
    <string name="versionchecklib_write_permission_deny">下载安装包需要读写文件权限。</string>
</resources>
