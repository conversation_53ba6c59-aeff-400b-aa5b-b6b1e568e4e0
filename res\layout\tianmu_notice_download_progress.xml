<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:id="@+id/tianmu_status_download_container"
    android:padding="10dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/tianmu_status_download_app_image"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:src="@drawable/tianmu_shape_ff0091ff_radius4"
        android:scaleType="centerCrop"
        android:layout_centerVertical="true"/>
    <ImageView
        android:id="@+id/tianmu_status_download_app_image_mask"
        android:visibility="visible"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:src="@drawable/tianmu_icon_notice_pause"
        android:scaleType="centerCrop"/>
    <TextView
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#000000"
        android:ellipsize="end"
        android:id="@+id/tianmu_status_download_app_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:text="标题"
        android:lines="1"
        android:layout_toLeftOf="@+id/tianmu_status_download_ll_btns"
        android:layout_toRightOf="@+id/tianmu_status_download_app_image"
        android:layout_alignTop="@+id/tianmu_status_download_app_image"/>
    <TextView
        android:textSize="12sp"
        android:id="@+id/tianmu_status_download_progress_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="2dp"
        android:text="0%"
        android:layout_above="@+id/tianmu_status_download_progress_bar"
        android:layout_alignLeft="@+id/tianmu_status_download_app_name"/>
    <TextView
        android:textSize="12sp"
        android:id="@+id/tianmu_status_download_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="暂停中"
        android:layout_alignRight="@+id/tianmu_status_download_app_name"
        android:layout_alignBottom="@+id/tianmu_status_download_progress_tv"/>
    <ProgressBar
        android:id="@+id/tianmu_status_download_progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="5dp"
        android:max="100"
        android:progress="0"
        android:layout_alignLeft="@+id/tianmu_status_download_app_name"
        android:layout_alignRight="@+id/tianmu_status_download_app_name"
        android:layout_alignBottom="@+id/tianmu_status_download_app_image"
        style="@style/tianmu_notice_progress_bar"/>
    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/tianmu_status_download_ll_btns"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true">
        <TextView
            android:textSize="10sp"
            android:textColor="#ffffff"
            android:gravity="center"
            android:id="@+id/tianmu_status_download_start"
            android:background="@drawable/tianmu_shape_ff0091ff_radius4"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:minWidth="50dp"
            android:text="继续"/>
        <TextView
            android:textSize="10sp"
            android:textColor="#ffffff"
            android:gravity="center"
            android:layout_gravity="center_vertical"
            android:id="@+id/tianmu_status_download_pause"
            android:background="@drawable/tianmu_shape_ff0091ff_radius4"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:minWidth="50dp"
            android:text="暂停"/>
        <TextView
            android:textSize="10sp"
            android:textColor="#ffffff"
            android:gravity="center"
            android:id="@+id/tianmu_status_download_stop"
            android:background="@drawable/tianmu_shape_ff0091ff_radius4"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:minWidth="50dp"
            android:text="取消"/>
    </LinearLayout>
</RelativeLayout>
