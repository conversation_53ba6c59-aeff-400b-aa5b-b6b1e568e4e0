<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/tianmu_interstitial_full_screen_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:id="@+id/tianmu_interstitial_fl_click"
        android:background="#fff"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignLeft="@+id/tianmu_interstitial_container"
        android:layout_alignTop="@+id/tianmu_interstitial_container"
        android:layout_alignRight="@+id/tianmu_interstitial_container"
        android:layout_alignBottom="@+id/tianmu_interstitial_container"/>
    <RelativeLayout
        android:id="@+id/tianmu_interstitial_container"
        android:background="#ffffff"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_centerVertical="true">
        <FrameLayout
            android:id="@+id/tianmu_interstitial_fl_container"
            android:layout_width="match_parent"
            android:layout_height="200dp"/>
        <TextView
            android:id="@+id/tianmu_tv_ad_target"
            android:src="@drawable/tianmu_icon_platform_icon"
            android:layout_alignRight="@+id/tianmu_interstitial_fl_container"
            android:layout_alignBottom="@+id/tianmu_interstitial_fl_container"
            style="@style/tianmu_ad_target_base_style"/>
        <TextView
            android:id="@+id/tianmu_banner_tv_ad_source"
            android:layout_alignLeft="@+id/tianmu_interstitial_fl_container"
            android:layout_alignTop="@+id/tianmu_interstitial_fl_container"
            style="@style/tianmu_ad_source_base_style"/>
        <TextView
            android:textSize="16sp"
            android:textColor="#333333"
            android:ellipsize="end"
            android:id="@+id/tianmu_interstitial_tv_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="23dp"
            android:layout_marginRight="12dp"
            android:text="@string/tianmu_custom_ad_title"
            android:maxLines="2"
            android:layout_below="@+id/tianmu_interstitial_fl_container"/>
        <TextView
            android:textSize="12sp"
            android:textColor="#b3b3b3"
            android:ellipsize="end"
            android:id="@+id/tianmu_interstitial_tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="4dp"
            android:layout_marginRight="12dp"
            android:text="@string/tianmu_custom_ad_content"
            android:lines="1"
            android:layout_below="@+id/tianmu_interstitial_tv_desc"/>
        <TextView
            android:textSize="18sp"
            android:textColor="#ffffff"
            android:gravity="center"
            android:id="@+id/tianmu_interstitial_tv_action"
            android:background="@drawable/tianmu_bg_background_1b72e6_radius4"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="24dp"
            android:layout_marginRight="12dp"
            android:layout_marginBottom="32dp"
            android:text="@string/tianmu_custom_ad_check_details"
            android:layout_below="@+id/tianmu_interstitial_tv_title"/>
    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/tianmu_rl_ad_interact"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignLeft="@+id/tianmu_interstitial_container"
        android:layout_alignTop="@+id/tianmu_interstitial_container"
        android:layout_alignRight="@+id/tianmu_interstitial_container"
        android:layout_alignBottom="@+id/tianmu_interstitial_container"/>
</RelativeLayout>
