<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:id="@+id/tianmu_library_rl_title"
        android:background="#ffffff"
        android:layout_width="match_parent"
        android:layout_height="48dp">
        <RelativeLayout
            android:id="@+id/tianmu_library_backlayout"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true">
            <ImageView
                android:id="@+id/tianmu_library_back_icon"
                android:padding="5dp"
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:layout_marginLeft="3dp"
                android:src="@drawable/tianmu_icon_back"
                android:scaleType="centerCrop"
                android:layout_centerVertical="true"/>
            <TextView
                android:textSize="16sp"
                android:textColor="#333333"
                android:id="@+id/tianmu_library_close_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="8dp"
                android:text="关闭"
                android:layout_toRightOf="@+id/tianmu_library_back_icon"
                android:layout_centerVertical="true"/>
        </RelativeLayout>
        <TextView
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:ellipsize="end"
            android:id="@+id/tianmu_library_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="100dp"
            android:layout_marginRight="100dp"
            android:text="@string/tianmu_custom_ad_application_permissions"
            android:maxLines="1"
            android:layout_centerInParent="true"/>
    </RelativeLayout>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="16sp"
                android:textColor="#555555"
                android:id="@+id/tianmu_app_permissions"
                android:padding="15dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text=""
                android:lineSpacingExtra="4dp"/>
        </LinearLayout>
    </ScrollView>
</LinearLayout>
