<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tianmu_library_rl_cover"
    android:background="#00000000"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:id="@+id/tianmu_library_fl_click"
        android:background="#63000000"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <ImageView
        android:id="@+id/tianmu_library_iv_close"
        android:background="@drawable/tianmu_shape_75cccccc_circle"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginTop="70dp"
        android:layout_marginRight="4dp"
        android:layout_marginBottom="7dp"
        android:src="@drawable/tianmu_reward_close"
        android:layout_above="@+id/tianmu_library_ll_ad_content"
        android:layout_alignRight="@+id/tianmu_library_ll_ad_content"/>
    <TextView
        android:textSize="12sp"
        android:textColor="#fff"
        android:gravity="center_vertical"
        android:id="@+id/tianmu_library_tv_count_down"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="11dp"
        android:text="@string/tianmu_reward_achieve_count_down"
        android:layout_toLeftOf="@+id/tianmu_library_iv_close"
        android:layout_alignTop="@+id/tianmu_library_iv_close"
        android:layout_alignBottom="@+id/tianmu_library_iv_close"/>
    <LinearLayout
        android:gravity="center"
        android:orientation="vertical"
        android:id="@+id/tianmu_library_ll_ad_content"
        android:background="@drawable/tianmu_shape_fff9f9f9_radius4"
        android:layout_width="343dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">
        <com.tianmu.biz.widget.roundimage.RoundedImageView
            android:id="@+id/tianmu_library_iv_app_icon"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginTop="24dp"
            android:scaleType="centerCrop"
            android:layout_centerHorizontal="true"/>
        <TextView
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center"
            android:id="@+id/tianmu_library_tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="17dp"
            android:layout_marginRight="16dp"
            android:text="@string/tianmu_custom_ad_title"
            android:singleLine="true"/>
        <TextView
            android:textSize="16sp"
            android:textColor="#555555"
            android:gravity="center"
            android:id="@+id/tianmu_library_tv_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="12dp"
            android:layout_marginRight="16dp"
            android:text="@string/tianmu_custom_ad_content"/>
        <TextView
            android:textSize="13sp"
            android:textColor="#e02020"
            android:gravity="center"
            android:id="@+id/tianmu_library_tv_function"
            android:paddingLeft="10dp"
            android:paddingTop="12dp"
            android:paddingRight="10dp"
            android:paddingBottom="12dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tianmu_custom_ad_download_now2"
            android:layout_centerHorizontal="true"/>
        <TextView
            android:textSize="14sp"
            android:textColor="#ffffff"
            android:gravity="center"
            android:id="@+id/tianmu_library_tv_continue_watch"
            android:background="@drawable/tianmu_shape_ffed3646_radius10"
            android:layout_width="182dp"
            android:layout_height="32dp"
            android:text="@string/tianmu_custom_ad_video_keep_watch"
            android:layout_below="@+id/tianmu_library_ll_ad_content"
            android:layout_centerHorizontal="true"/>
        <TextView
            android:textSize="13sp"
            android:textColor="#999999"
            android:gravity="center"
            android:id="@+id/tianmu_library_tv_exit"
            android:paddingLeft="10dp"
            android:paddingTop="12dp"
            android:paddingRight="10dp"
            android:paddingBottom="16dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tianmu_custom_ad_video_continue_exit"
            android:layout_below="@+id/tianmu_library_ll_ad_content"
            android:layout_centerHorizontal="true"/>
    </LinearLayout>
</RelativeLayout>
