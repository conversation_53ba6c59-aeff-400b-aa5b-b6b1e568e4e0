<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:id="@+id/tianmu_library_rl_ad_content"
        android:background="@drawable/tianmu_shape_ffffffff_radius12"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:layout_width="match_parent"
        android:layout_height="72dp"
        android:layout_centerHorizontal="true">
        <com.tianmu.biz.widget.roundimage.RoundedImageView
            android:id="@+id/tianmu_library_iv_image"
            android:visibility="visible"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginLeft="4dp"
            android:scaleType="centerCrop"/>
        <TextView
            android:textSize="14sp"
            android:textColor="#ffffff"
            android:gravity="center"
            android:id="@+id/tianmu_library_tv_action"
            android:background="@drawable/tianmu_shape_ff0091ff_radius4"
            android:padding="8dp"
            android:visibility="visible"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginRight="8dp"
            android:text="@string/tianmu_custom_ad_download_now"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"/>
        <TextView
            android:textSize="16sp"
            android:textColor="#000000"
            android:id="@+id/tianmu_library_tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="10dp"
            android:singleLine="true"
            android:layout_toLeftOf="@+id/tianmu_library_tv_action"
            android:layout_toRightOf="@+id/tianmu_library_iv_image"
            android:layout_alignTop="@+id/tianmu_library_iv_image"/>
        <TextView
            android:textSize="13sp"
            android:textColor="#666666"
            android:id="@+id/tianmu_library_tv_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:singleLine="true"
            android:layout_toLeftOf="@+id/tianmu_library_tv_action"
            android:layout_below="@+id/tianmu_library_tv_title"
            android:layout_alignLeft="@+id/tianmu_library_tv_title"
            android:layout_alignBottom="@+id/tianmu_library_iv_image"/>
    </RelativeLayout>
</RelativeLayout>
