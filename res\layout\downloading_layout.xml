<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:padding="20dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <androidx.core.widget.ContentLoadingProgressBar
        android:id="@+id/pb"
        android:layout_width="match_parent"
        android:layout_height="15dp"
        android:max="100"
        android:progress="40"
        style="?android:attr/progressBarStyleHorizontal"/>
    <RelativeLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="15sp"
            android:textColor="@color/versionchecklib_theme_color"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/versionchecklib_downloading"/>
        <TextView
            android:textSize="15sp"
            android:textColor="@color/versionchecklib_theme_color"
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/versionchecklib_progress"
            android:layout_alignParentRight="true"/>
    </RelativeLayout>
</LinearLayout>
