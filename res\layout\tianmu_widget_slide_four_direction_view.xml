<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:orientation="vertical"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/tianmu_sfd_iv_bg"
            android:layout_width="147dp"
            android:layout_height="147dp"
            android:src="@drawable/tianmu_icon_direction_bg"/>
        <ImageView
            android:id="@+id/tianmu_sfd_iv_finger"
            android:layout_width="27dp"
            android:layout_height="27dp"
            android:src="@drawable/tianmu_sliding_screen_figer"/>
    </RelativeLayout>
    <include layout="@layout/tianmu_include_interaction_tips_view2"/>
</LinearLayout>
