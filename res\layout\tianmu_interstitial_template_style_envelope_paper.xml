<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:background="@drawable/tianmu_shape_ffffff_radius20"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:id="@+id/tianmu_interstitial_envelope_paper_fl_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="centerCrop">
        <com.tianmu.biz.widget.roundimage.RoundedImageView
            android:id="@+id/tianmu_interstitial_envelope_paper_iv_cover"
            android:layout_width="match_parent"
            android:layout_height="175dp"
            android:scaleType="centerCrop"/>
    </FrameLayout>
    <TextView
        android:textSize="16sp"
        android:textColor="#222222"
        android:id="@+id/tianmu_interstitial_envelope_paper_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"/>
    <com.tianmu.biz.widget.roundimage.RoundedImageView
        android:id="@+id/tianmu_interstitial_envelope_paper_iv_mini_image"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="8dp"
        android:scaleType="centerCrop"/>
    <TextView
        android:textSize="14sp"
        android:textColor="#666666"
        android:id="@+id/tianmu_interstitial_envelope_paper_tv_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"/>
</LinearLayout>
