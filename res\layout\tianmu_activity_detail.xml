<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/tianmu_library_ll_parent_container"
        android:background="#eeeeee"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentBottom="true">
        <RelativeLayout
            android:id="@+id/tianmu_library_rl_title"
            android:background="#ffffff"
            android:layout_width="match_parent"
            android:layout_height="48dp">
            <RelativeLayout
                android:id="@+id/tianmu_library_backlayout"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerVertical="true">
                <ImageView
                    android:id="@+id/tianmu_library_back_icon"
                    android:padding="5dp"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:layout_marginLeft="3dp"
                    android:src="@drawable/tianmu_icon_back"
                    android:scaleType="centerCrop"
                    android:layout_centerVertical="true"/>
                <TextView
                    android:textSize="16sp"
                    android:textColor="#333333"
                    android:id="@+id/tianmu_library_close_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:text="关闭"
                    android:layout_toRightOf="@+id/tianmu_library_back_icon"
                    android:layout_centerVertical="true"/>
            </RelativeLayout>
            <TextView
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:ellipsize="end"
                android:id="@+id/tianmu_library_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="100dp"
                android:layout_marginRight="100dp"
                android:text=""
                android:maxLines="1"
                android:layout_centerInParent="true"/>
        </RelativeLayout>
        <FrameLayout
            android:id="@+id/tianmu_library_content"
            android:background="#ffffff"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="1dp">
            <FrameLayout
                android:id="@+id/tianmu_library_layout_webView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
            <FrameLayout
                android:id="@+id/tianmu_library_video_fullView"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
            <ProgressBar
                android:id="@+id/tianmu_library_pb_progress"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:max="100"
                android:indeterminateOnly="false"
                style="?android:attr/progressBarStyleHorizontal"/>
            <ProgressBar
                android:layout_gravity="center"
                android:id="@+id/tianmu_library_json_loading"
                android:visibility="gone"
                android:layout_width="32dp"
                android:layout_height="32dp"/>
            <TextView
                android:layout_gravity="center"
                android:id="@+id/tianmu_library_tv_tips"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </FrameLayout>
    </LinearLayout>
</RelativeLayout>
